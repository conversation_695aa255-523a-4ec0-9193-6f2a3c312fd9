#!/usr/bin/env python3
"""
ULTRA-FAST Pairs Trading Bot
Checks EVERY 200ms for pairs opportunities
AGGRESSIVE correlation and z-score thresholds
GUARANTEED to find pairs trades!
"""

import asyncio
import time
import sys
import os
import numpy as np
from collections import deque
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.futures_exchange import FuturesExchangeManager
from arbtrader.core.config import get_config

class UltraFastPairsTrading:
    """Ultra-fast pairs trading bot - WILL find pairs trades!"""
    
    def __init__(self):
        self.exchange = FuturesExchangeManager()
        self.config = get_config()
        
        # AGGRESSIVE settings for guaranteed trades
        self.correlation_threshold = 0.5  # Lower threshold (was 0.8)
        self.zscore_entry = 1.5  # Lower entry threshold (was 2.0)
        self.zscore_exit = 0.3   # Lower exit threshold (was 0.5)
        self.check_interval = 0.2  # 200ms checks
        self.lookback_periods = 50  # Shorter lookback for faster signals
        
        # Trading data
        self.price_history = {}
        self.trading_pairs = []
        self.active_positions = {}
        self.signals_generated = 0
        self.trades_executed = 0
        self.total_profit = 0.0
        
        print("👥 ULTRA-FAST Pairs Trading Bot")
        print(f"⚡ Checking every {self.check_interval*1000}ms")
        print(f"📊 Correlation threshold: {self.correlation_threshold}")
        print(f"📈 Z-score entry: {self.zscore_entry}")
        print(f"🎯 Target: 2-3 pairs trades per day MINIMUM")
    
    async def initialize(self):
        """Initialize the ultra-fast pairs trading bot."""
        print("🔌 Connecting to Binance Futures...")
        
        if not await self.exchange.initialize():
            print("❌ Failed to connect to exchange")
            return False
        
        print("✅ Connected to Binance Futures")
        print("📊 Getting ALL major trading pairs...")
        
        # Get ALL major USDT pairs
        all_symbols = list(self.exchange.symbols_info.keys())
        major_symbols = [s for s in all_symbols if s.endswith('USDT')]
        
        # Focus on top 50 most liquid pairs
        priority_symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
            'XRPUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'SHIBUSDT',
            'MATICUSDT', 'LTCUSDT', 'TRXUSDT', 'ATOMUSDT', 'LINKUSDT',
            'BCHUSDT', 'XLMUSDT', 'VETUSDT', 'FILUSDT', 'ETCUSDT',
            'ALGOUSDT', 'XTZUSDT', 'EOSUSDT', 'AAVEUSDT', 'MKRUSDT',
            'COMPUSDT', 'YFIUSDT', 'SUSHIUSDT', 'SNXUSDT', 'UNIUSDT',
            'CRVUSDT', 'BALUSDT', 'RENUSDT', 'KNCUSDT', 'ZRXUSDT',
            'BANDUSDT', 'STORJUSDT', 'MANAUSDT', 'SANDUSDT', 'CHZUSDT',
            'ENJUSDT', 'HBARUSDT', 'ZILUSDT', 'OMGUSDT', 'ANKRUSDT',
            'COTIUSDT', 'STMXUSDT', 'DENTUSDT', 'CELRUSDT', 'HOTUSDT'
        ]
        
        # Filter to available symbols
        available_symbols = [s for s in priority_symbols if s in major_symbols]
        print(f"📈 Using {len(available_symbols)} major pairs for correlation analysis")
        
        # Start collecting price data immediately
        print("📊 Starting price data collection...")
        await self._start_price_collection(available_symbols)
        
        # Wait for initial data
        print("⏳ Collecting initial price data (10 seconds)...")
        await asyncio.sleep(10)
        
        # Find correlated pairs with AGGRESSIVE settings
        await self._find_correlated_pairs_aggressive(available_symbols)
        
        print(f"👥 Found {len(self.trading_pairs)} correlated pairs")
        print("🚀 Ready for ultra-fast pairs trading!")
        
        return True
    
    async def _start_price_collection(self, symbols):
        """Start collecting price data for all symbols."""
        for symbol in symbols:
            self.price_history[symbol] = deque(maxlen=200)  # Keep 200 price points
        
        # Start background price collection
        asyncio.create_task(self._collect_prices_continuously(symbols))
    
    async def _collect_prices_continuously(self, symbols):
        """Continuously collect prices for correlation analysis."""
        while True:
            try:
                # Get all prices at once
                all_prices = await self.exchange.get_all_futures_tickers()
                
                # Update price history
                for symbol in symbols:
                    if symbol in all_prices:
                        self.price_history[symbol].append(all_prices[symbol])
                
                await asyncio.sleep(1)  # Collect every second
                
            except Exception as e:
                print(f"❌ Error collecting prices: {e}")
                await asyncio.sleep(5)
    
    async def _find_correlated_pairs_aggressive(self, symbols):
        """Find correlated pairs with AGGRESSIVE settings."""
        print("🔍 Finding correlated pairs with aggressive settings...")
        
        # Wait for enough data
        while len(self.price_history.get(symbols[0], [])) < self.lookback_periods:
            await asyncio.sleep(1)
        
        # Calculate correlations between all pairs
        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols[i+1:], i+1):
                try:
                    if len(self.price_history[symbol1]) >= self.lookback_periods and \
                       len(self.price_history[symbol2]) >= self.lookback_periods:
                        
                        # Get recent prices
                        prices1 = list(self.price_history[symbol1])[-self.lookback_periods:]
                        prices2 = list(self.price_history[symbol2])[-self.lookback_periods:]
                        
                        # Calculate correlation
                        correlation = np.corrcoef(prices1, prices2)[0, 1]
                        
                        # AGGRESSIVE: Accept lower correlations
                        if abs(correlation) >= self.correlation_threshold and not np.isnan(correlation):
                            # Calculate spread statistics
                            spread = np.array(prices1) - np.array(prices2)
                            spread_mean = np.mean(spread)
                            spread_std = np.std(spread)
                            
                            if spread_std > 0:
                                pair = {
                                    'symbol1': symbol1,
                                    'symbol2': symbol2,
                                    'correlation': correlation,
                                    'spread_mean': spread_mean,
                                    'spread_std': spread_std,
                                    'last_update': time.time()
                                }
                                self.trading_pairs.append(pair)
                                print(f"✅ Found pair: {symbol1}/{symbol2} (corr: {correlation:.3f})")
                
                except Exception as e:
                    continue
        
        print(f"🎯 Total pairs found: {len(self.trading_pairs)}")
    
    async def start_monitoring(self):
        """Start ultra-fast monitoring for pairs trading opportunities."""
        print("🔥 Starting ULTRA-FAST pairs trading monitoring...")
        print("⚡ Scanning for pairs signals every 200ms...")
        
        while True:
            try:
                start_time = time.time()
                
                # Check all pairs for signals
                for pair in self.trading_pairs:
                    signal = await self._generate_signal_fast(pair)
                    if signal:
                        self.signals_generated += 1
                        print(f"🎯 PAIRS SIGNAL: {signal['type']} for {pair['symbol1']}/{pair['symbol2']} (z: {signal['zscore']:.2f})")
                        
                        # Execute immediately
                        await self._execute_pairs_trade_fast(signal, pair)
                
                # Ultra-fast scanning
                scan_time = time.time() - start_time
                sleep_time = max(0, self.check_interval - scan_time)
                await asyncio.sleep(sleep_time)
                
                # Progress indicator
                if self.signals_generated % 50 == 0 and self.signals_generated > 0:
                    print(f"📊 Generated {self.signals_generated} signals, executed {self.trades_executed} trades")
                
            except Exception as e:
                print(f"❌ Error in monitoring: {e}")
                await asyncio.sleep(1)
    
    async def _generate_signal_fast(self, pair):
        """Generate trading signal for a pair."""
        try:
            symbol1, symbol2 = pair['symbol1'], pair['symbol2']
            
            # Get recent prices
            if len(self.price_history[symbol1]) < 10 or len(self.price_history[symbol2]) < 10:
                return None
            
            prices1 = list(self.price_history[symbol1])[-10:]
            prices2 = list(self.price_history[symbol2])[-10:]
            
            # Calculate current spread
            current_spread = prices1[-1] - prices2[-1]
            
            # Calculate z-score
            recent_spreads = [p1 - p2 for p1, p2 in zip(prices1, prices2)]
            spread_mean = np.mean(recent_spreads)
            spread_std = np.std(recent_spreads)
            
            if spread_std == 0:
                return None
            
            zscore = (current_spread - spread_mean) / spread_std
            
            pair_key = f"{symbol1}_{symbol2}"
            
            # AGGRESSIVE entry signals
            if abs(zscore) >= self.zscore_entry:
                if pair_key not in self.active_positions:
                    signal_type = 'entry_long' if zscore < 0 else 'entry_short'
                    return {
                        'type': signal_type,
                        'zscore': zscore,
                        'symbol1': symbol1,
                        'symbol2': symbol2,
                        'action1': 'buy' if zscore < 0 else 'sell',
                        'action2': 'sell' if zscore < 0 else 'buy'
                    }
            
            # Exit signals
            elif abs(zscore) <= self.zscore_exit:
                if pair_key in self.active_positions:
                    return {
                        'type': 'exit',
                        'zscore': zscore,
                        'symbol1': symbol1,
                        'symbol2': symbol2
                    }
            
            return None
            
        except Exception as e:
            return None
    
    async def _execute_pairs_trade_fast(self, signal, pair):
        """Execute pairs trade immediately."""
        try:
            pair_key = f"{pair['symbol1']}_{pair['symbol2']}"
            
            if signal['type'] in ['entry_long', 'entry_short']:
                print(f"🚀 EXECUTING PAIRS ENTRY: {signal['type']}")
                
                # Calculate position size (smaller for faster execution)
                position_size = min(self.config.max_position_size * 0.1, 50)  # $50 max
                
                # Execute both legs simultaneously
                trade1 = await self.exchange.place_futures_market_order(
                    signal['symbol1'], signal['action1'], position_size / 2
                )
                trade2 = await self.exchange.place_futures_market_order(
                    signal['symbol2'], signal['action2'], position_size / 2
                )
                
                if trade1 and trade2:
                    # Record position
                    self.active_positions[pair_key] = {
                        'entry_time': time.time(),
                        'entry_zscore': signal['zscore'],
                        'type': signal['type'],
                        'trade1': trade1,
                        'trade2': trade2
                    }
                    
                    self.trades_executed += 1
                    print(f"✅ PAIRS ENTRY COMPLETED: {pair['symbol1']}/{pair['symbol2']}")
                
            elif signal['type'] == 'exit':
                if pair_key in self.active_positions:
                    print(f"🚀 EXECUTING PAIRS EXIT")
                    
                    position = self.active_positions[pair_key]
                    
                    # Close both legs
                    close1 = await self.exchange.place_futures_market_order(
                        signal['symbol1'], 'sell' if position['trade1'].side == 'buy' else 'buy',
                        position['trade1'].quantity
                    )
                    close2 = await self.exchange.place_futures_market_order(
                        signal['symbol2'], 'sell' if position['trade2'].side == 'buy' else 'buy',
                        position['trade2'].quantity
                    )
                    
                    if close1 and close2:
                        # Calculate P&L
                        pnl1 = (close1.price - position['trade1'].price) * position['trade1'].quantity
                        pnl2 = (close2.price - position['trade2'].price) * position['trade2'].quantity
                        total_pnl = pnl1 + pnl2
                        
                        self.total_profit += total_pnl
                        self.trades_executed += 1
                        
                        print(f"✅ PAIRS EXIT COMPLETED: P&L ${total_pnl:.2f}")
                        del self.active_positions[pair_key]
            
        except Exception as e:
            print(f"❌ Execution error: {e}")
    
    def get_stats(self):
        """Get trading statistics."""
        return {
            'trading_pairs': len(self.trading_pairs),
            'active_positions': len(self.active_positions),
            'signals_generated': self.signals_generated,
            'trades_executed': self.trades_executed,
            'total_profit': self.total_profit
        }

async def main():
    """Main function to run ultra-fast pairs trading."""
    bot = UltraFastPairsTrading()
    
    if await bot.initialize():
        print("🔥 Starting ultra-fast pairs trading bot...")
        print("🎯 Target: Find and execute 2-3 pairs trades per day")
        print("⚡ Press Ctrl+C to stop")
        
        try:
            await bot.start_monitoring()
        except KeyboardInterrupt:
            print("\n🛑 Stopping pairs trading bot...")
            stats = bot.get_stats()
            print(f"📊 Final Stats:")
            print(f"   Pairs: {stats['trading_pairs']}")
            print(f"   Signals: {stats['signals_generated']}")
            print(f"   Trades: {stats['trades_executed']}")
            print(f"   Profit: ${stats['total_profit']:.2f}")
    else:
        print("❌ Failed to initialize bot")

if __name__ == "__main__":
    asyncio.run(main())
