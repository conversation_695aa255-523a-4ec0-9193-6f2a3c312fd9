# Makefile for ArbTrader

.PHONY: help install test lint format clean run-paper run-live

help:
	@echo "Available commands:"
	@echo "  install     Install dependencies and setup environment"
	@echo "  test        Run test suite"
	@echo "  lint        Run code linting"
	@echo "  format      Format code with black"
	@echo "  clean       Clean up temporary files"
	@echo "  run-paper   Run in paper trading mode"
	@echo "  run-live    Run in live trading mode"
	@echo "  config      Check configuration"

install:
	pip install -r requirements.txt
	pip install -e .

test:
	pytest tests/ -v

lint:
	flake8 arbtrader/
	mypy arbtrader/

format:
	black arbtrader/ tests/

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/

run-paper:
	python -m arbtrader.cli start --paper

run-live:
	python -m arbtrader.cli start --live

config:
	python -m arbtrader.cli config-check

setup-env:
	cp .env.example .env
	@echo "Please edit .env file with your API keys"

docker-build:
	docker build -t arbtrader .

docker-run:
	docker run -it --env-file .env arbtrader
