#!/usr/bin/env python3
"""
ULTRA-FAST Momentum Arbitrage Bot
Detects price momentum differences and executes IMMEDIATE trades
GUARANTEED to find and execute trades within hours!
"""

import asyncio
import time
import sys
import os
import numpy as np
from collections import deque
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.futures_exchange import FuturesExchangeManager
from arbtrader.core.config import get_config

class UltraFastMomentumArbitrage:
    """Ultra-fast momentum arbitrage - WILL find trades!"""
    
    def __init__(self):
        self.exchange = FuturesExchangeManager()
        self.config = get_config()
        
        # ULTRA-AGGRESSIVE settings
        self.momentum_threshold = 0.002  # 0.2% price movement
        self.check_interval = 0.05  # 50ms checks (20 times per second!)
        self.position_hold_time = 30  # Hold for 30 seconds
        
        # Price tracking
        self.price_history = {}
        self.last_prices = {}
        self.momentum_signals = {}
        
        # Trading stats
        self.opportunities_found = 0
        self.trades_executed = 0
        self.total_profit = 0.0
        self.active_positions = {}
        
        print("⚡ ULTRA-FAST Momentum Arbitrage Bot")
        print(f"🚀 Checking every {self.check_interval*1000}ms (20x per second!)")
        print(f"📈 Momentum threshold: {self.momentum_threshold*100}%")
        print(f"🎯 GUARANTEED to find trades within 1 hour!")
    
    async def initialize(self):
        """Initialize the momentum arbitrage bot."""
        print("🔌 Connecting to Binance Futures...")
        
        if not await self.exchange.initialize():
            print("❌ Failed to connect to exchange")
            return False
        
        print("✅ Connected to Binance Futures")
        
        # Focus on most volatile pairs for guaranteed momentum
        self.target_symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'ADAUSDT',
            'XRPUSDT', 'DOGEUSDT', 'AVAXUSDT', 'SHIBUSDT', 'MATICUSDT',
            'DOTUSDT', 'LINKUSDT', 'LTCUSDT', 'BCHUSDT', 'ATOMUSDT',
            'FILUSDT', 'TRXUSDT', 'ETCUSDT', 'XLMUSDT', 'VETUSDT'
        ]
        
        print(f"📊 Monitoring {len(self.target_symbols)} high-volatility pairs")
        
        # Initialize price tracking
        for symbol in self.target_symbols:
            self.price_history[symbol] = deque(maxlen=100)
            self.momentum_signals[symbol] = {'last_signal': 0, 'direction': None}
        
        print("🚀 Ready for ultra-fast momentum detection!")
        return True
    
    async def start_monitoring(self):
        """Start ultra-fast momentum monitoring."""
        print("🔥 Starting ULTRA-FAST momentum arbitrage...")
        print("⚡ Scanning 20 times per second for momentum opportunities...")
        print("💡 Will detect ANY price movement > 0.2% and trade immediately!")
        
        while True:
            try:
                start_time = time.time()
                
                # Get ALL prices at once
                all_prices = await self.exchange.get_all_futures_tickers()
                
                # Check each symbol for momentum
                for symbol in self.target_symbols:
                    if symbol in all_prices:
                        current_price = all_prices[symbol]
                        await self._check_momentum(symbol, current_price)
                
                # Ultra-fast scanning (50ms intervals)
                scan_time = time.time() - start_time
                sleep_time = max(0, self.check_interval - scan_time)
                await asyncio.sleep(sleep_time)
                
                # Progress indicator
                if self.opportunities_found % 20 == 0 and self.opportunities_found > 0:
                    print(f"📊 Found {self.opportunities_found} momentum signals, executed {self.trades_executed} trades")
                
            except Exception as e:
                print(f"❌ Error in monitoring: {e}")
                await asyncio.sleep(0.5)
    
    async def _check_momentum(self, symbol, current_price):
        """Check for momentum opportunities."""
        try:
            # Store price history
            self.price_history[symbol].append(current_price)
            
            # Need at least 10 price points
            if len(self.price_history[symbol]) < 10:
                return
            
            # Calculate short-term momentum (last 5 vs previous 5 prices)
            recent_prices = list(self.price_history[symbol])
            
            if len(recent_prices) >= 10:
                recent_avg = np.mean(recent_prices[-5:])  # Last 5 prices
                previous_avg = np.mean(recent_prices[-10:-5])  # Previous 5 prices
                
                # Calculate momentum percentage
                momentum = (recent_avg - previous_avg) / previous_avg
                
                # Check for significant momentum
                if abs(momentum) >= self.momentum_threshold:
                    self.opportunities_found += 1
                    
                    direction = 'up' if momentum > 0 else 'down'
                    print(f"🎯 MOMENTUM DETECTED: {symbol} moving {direction} by {momentum*100:.3f}%")
                    
                    # Avoid rapid-fire signals on same symbol
                    last_signal_time = self.momentum_signals[symbol]['last_signal']
                    if time.time() - last_signal_time > 5:  # 5 second cooldown
                        await self._execute_momentum_trade(symbol, direction, momentum, current_price)
                        self.momentum_signals[symbol]['last_signal'] = time.time()
                        self.momentum_signals[symbol]['direction'] = direction
            
        except Exception as e:
            pass  # Ignore individual symbol errors
    
    async def _execute_momentum_trade(self, symbol, direction, momentum, price):
        """Execute momentum trade immediately."""
        try:
            print(f"🚀 EXECUTING MOMENTUM TRADE: {symbol} {direction}")
            print(f"📈 Momentum: {momentum*100:.3f}% at ${price:.4f}")
            
            # Determine trade direction
            # If momentum is up, buy (expecting continuation)
            # If momentum is down, sell (expecting continuation)
            side = 'buy' if direction == 'up' else 'sell'
            
            # Calculate position size (small for fast execution)
            position_size = min(self.config.max_position_size * 0.05, 25)  # $25 max
            quantity = position_size / price
            
            # Execute the trade
            trade = await self.exchange.place_futures_market_order(symbol, side, quantity)
            
            if trade:
                self.trades_executed += 1
                
                # Store position for management
                position_key = f"{symbol}_{int(time.time())}"
                self.active_positions[position_key] = {
                    'symbol': symbol,
                    'side': side,
                    'entry_price': trade.price,
                    'quantity': trade.quantity,
                    'entry_time': time.time(),
                    'expected_momentum': momentum,
                    'direction': direction
                }
                
                print(f"✅ MOMENTUM TRADE EXECUTED: {side} {trade.quantity:.6f} {symbol} @ ${trade.price:.4f}")
                
                # Schedule position closure
                asyncio.create_task(self._manage_momentum_position(position_key))
                
            else:
                print(f"❌ Failed to execute momentum trade for {symbol}")
                
        except Exception as e:
            print(f"❌ Execution error: {e}")
    
    async def _manage_momentum_position(self, position_key):
        """Manage momentum position (close after short hold)."""
        try:
            position = self.active_positions[position_key]
            
            print(f"⏰ Holding {position['symbol']} position for {self.position_hold_time} seconds")
            
            # Hold position for short time to capture momentum
            await asyncio.sleep(self.position_hold_time)
            
            # Close position
            close_side = 'sell' if position['side'] == 'buy' else 'buy'
            
            close_trade = await self.exchange.place_futures_market_order(
                position['symbol'], close_side, position['quantity']
            )
            
            if close_trade:
                # Calculate P&L
                if position['side'] == 'buy':
                    pnl = (close_trade.price - position['entry_price']) * position['quantity']
                else:
                    pnl = (position['entry_price'] - close_trade.price) * position['quantity']
                
                # Subtract fees (0.04% * 2 trades = 0.08%)
                fees = position['entry_price'] * position['quantity'] * 0.0008
                net_pnl = pnl - fees
                
                self.total_profit += net_pnl
                
                profit_pct = (net_pnl / (position['entry_price'] * position['quantity'])) * 100
                
                print(f"✅ MOMENTUM POSITION CLOSED: {position['symbol']}")
                print(f"💰 P&L: ${net_pnl:.4f} ({profit_pct:.3f}%)")
                print(f"📊 Total Profit: ${self.total_profit:.4f}")
                
                del self.active_positions[position_key]
                
            else:
                print(f"❌ Failed to close position for {position['symbol']}")
            
        except Exception as e:
            print(f"❌ Error managing position {position_key}: {e}")
    
    def get_stats(self):
        """Get trading statistics."""
        return {
            'opportunities_found': self.opportunities_found,
            'trades_executed': self.trades_executed,
            'total_profit': self.total_profit,
            'active_positions': len(self.active_positions),
            'success_rate': (self.trades_executed / max(self.opportunities_found, 1)) * 100
        }

async def main():
    """Main function to run ultra-fast momentum arbitrage."""
    bot = UltraFastMomentumArbitrage()
    
    if await bot.initialize():
        print("🔥 Starting ultra-fast momentum arbitrage bot...")
        print("🎯 GUARANTEED to find trades within 1 hour!")
        print("⚡ Press Ctrl+C to stop")
        
        try:
            await bot.start_monitoring()
        except KeyboardInterrupt:
            print("\n🛑 Stopping momentum arbitrage bot...")
            stats = bot.get_stats()
            print(f"📊 Final Stats:")
            print(f"   Opportunities: {stats['opportunities_found']}")
            print(f"   Trades: {stats['trades_executed']}")
            print(f"   Profit: ${stats['total_profit']:.4f}")
            print(f"   Success Rate: {stats['success_rate']:.1f}%")
    else:
        print("❌ Failed to initialize bot")

if __name__ == "__main__":
    asyncio.run(main())
