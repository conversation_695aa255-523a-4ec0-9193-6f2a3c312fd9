"""Comprehensive risk management system."""

import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np
import pandas as pd

from ..core.exchange import ExchangeManager, Balance
from ..core.config import config
from ..core.logger import get_logger, log_error
from ..data.market_data import MarketDataManager

logger = get_logger(__name__)


class RiskLevel(Enum):
    """Risk level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskMetrics:
    """Risk metrics data structure."""
    total_exposure: float
    portfolio_value: float
    daily_pnl: float
    daily_pnl_percentage: float
    max_drawdown: float
    var_95: float  # Value at Risk 95%
    sharpe_ratio: float
    risk_level: RiskLevel
    timestamp: float


@dataclass
class PositionRisk:
    """Individual position risk assessment."""
    symbol: str
    position_size: float
    market_value: float
    unrealized_pnl: float
    risk_percentage: float
    volatility: float
    var_95: float
    risk_level: RiskLevel


@dataclass
class RiskLimit:
    """Risk limit configuration."""
    name: str
    limit_type: str  # 'percentage', 'absolute'
    threshold: float
    current_value: float
    is_breached: bool
    severity: RiskLevel


class RiskManager:
    """Comprehensive risk management system."""
    
    def __init__(self, exchange_manager: ExchangeManager, market_data_manager: MarketDataManager):
        self.exchange_manager = exchange_manager
        self.market_data_manager = market_data_manager
        
        # Risk tracking
        self.portfolio_history: List[float] = []
        self.daily_pnl_history: List[float] = []
        self.risk_limits: Dict[str, RiskLimit] = {}
        
        # Position tracking
        self.position_risks: Dict[str, PositionRisk] = {}
        self.total_exposure = 0.0
        self.portfolio_value = 0.0
        
        # Risk metrics
        self.current_risk_level = RiskLevel.LOW
        self.last_risk_check = 0
        
        self._initialize_risk_limits()
    
    def _initialize_risk_limits(self):
        """Initialize risk limits based on configuration."""
        self.risk_limits = {
            'max_daily_loss': RiskLimit(
                name='Maximum Daily Loss',
                limit_type='percentage',
                threshold=config.max_daily_loss * 100,
                current_value=0.0,
                is_breached=False,
                severity=RiskLevel.CRITICAL
            ),
            'max_position_size': RiskLimit(
                name='Maximum Position Size',
                limit_type='absolute',
                threshold=config.max_position_size,
                current_value=0.0,
                is_breached=False,
                severity=RiskLevel.HIGH
            ),
            'max_total_exposure': RiskLimit(
                name='Maximum Total Exposure',
                limit_type='percentage',
                threshold=80.0,  # 80% of portfolio
                current_value=0.0,
                is_breached=False,
                severity=RiskLevel.HIGH
            ),
            'max_correlation_exposure': RiskLimit(
                name='Maximum Correlation Exposure',
                limit_type='percentage',
                threshold=50.0,  # 50% in correlated assets
                current_value=0.0,
                is_breached=False,
                severity=RiskLevel.MEDIUM
            )
        }
    
    async def update_risk_metrics(self) -> RiskMetrics:
        """Update and calculate current risk metrics."""
        try:
            # Get current portfolio value
            await self._update_portfolio_value()
            
            # Calculate daily P&L
            daily_pnl = self._calculate_daily_pnl()
            daily_pnl_percentage = (daily_pnl / self.portfolio_value * 100) if self.portfolio_value > 0 else 0
            
            # Update P&L history
            self.daily_pnl_history.append(daily_pnl_percentage)
            if len(self.daily_pnl_history) > 252:  # Keep 1 year of data
                self.daily_pnl_history.pop(0)
            
            # Calculate risk metrics
            max_drawdown = self._calculate_max_drawdown()
            var_95 = self._calculate_var_95()
            sharpe_ratio = self._calculate_sharpe_ratio()
            
            # Determine risk level
            risk_level = self._assess_risk_level(daily_pnl_percentage, max_drawdown, var_95)
            
            # Update risk limits
            await self._update_risk_limits(daily_pnl_percentage)
            
            risk_metrics = RiskMetrics(
                total_exposure=self.total_exposure,
                portfolio_value=self.portfolio_value,
                daily_pnl=daily_pnl,
                daily_pnl_percentage=daily_pnl_percentage,
                max_drawdown=max_drawdown,
                var_95=var_95,
                sharpe_ratio=sharpe_ratio,
                risk_level=risk_level,
                timestamp=time.time()
            )
            
            self.current_risk_level = risk_level
            self.last_risk_check = time.time()
            
            return risk_metrics
            
        except Exception as e:
            log_error("Error updating risk metrics", e)
            return None
    
    async def _update_portfolio_value(self):
        """Update current portfolio value."""
        try:
            balances = await self.exchange_manager.get_account_balance()
            total_value = 0.0
            
            for balance in balances:
                if balance.total > 0:
                    if balance.asset == 'USDT':
                        # USDT is base currency
                        total_value += balance.total
                    else:
                        # Convert to USDT value
                        symbol = f"{balance.asset}USDT"
                        ticker = self.market_data_manager.get_ticker(symbol)
                        if ticker:
                            total_value += balance.total * ticker.price
            
            self.portfolio_value = total_value
            self.portfolio_history.append(total_value)
            
            if len(self.portfolio_history) > 1000:  # Keep reasonable history
                self.portfolio_history.pop(0)
                
        except Exception as e:
            log_error("Error updating portfolio value", e)
    
    def _calculate_daily_pnl(self) -> float:
        """Calculate daily P&L."""
        if len(self.portfolio_history) < 2:
            return 0.0
        
        return self.portfolio_history[-1] - self.portfolio_history[-2]
    
    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown."""
        if len(self.portfolio_history) < 2:
            return 0.0
        
        portfolio_array = np.array(self.portfolio_history)
        peak = np.maximum.accumulate(portfolio_array)
        drawdown = (portfolio_array - peak) / peak * 100
        
        return float(np.min(drawdown))
    
    def _calculate_var_95(self) -> float:
        """Calculate Value at Risk at 95% confidence level."""
        if len(self.daily_pnl_history) < 30:
            return 0.0
        
        return float(np.percentile(self.daily_pnl_history, 5))
    
    def _calculate_sharpe_ratio(self) -> float:
        """Calculate Sharpe ratio."""
        if len(self.daily_pnl_history) < 30:
            return 0.0
        
        returns = np.array(self.daily_pnl_history)
        excess_returns = returns - 0.01  # Assume 1% risk-free rate (daily)
        
        if np.std(excess_returns) == 0:
            return 0.0
        
        return float(np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252))
    
    def _assess_risk_level(self, daily_pnl_pct: float, max_drawdown: float, var_95: float) -> RiskLevel:
        """Assess overall risk level."""
        risk_score = 0
        
        # Daily P&L risk
        if daily_pnl_pct < -3:
            risk_score += 3
        elif daily_pnl_pct < -1:
            risk_score += 2
        elif daily_pnl_pct < -0.5:
            risk_score += 1
        
        # Drawdown risk
        if max_drawdown < -10:
            risk_score += 3
        elif max_drawdown < -5:
            risk_score += 2
        elif max_drawdown < -2:
            risk_score += 1
        
        # VaR risk
        if var_95 < -5:
            risk_score += 3
        elif var_95 < -3:
            risk_score += 2
        elif var_95 < -1:
            risk_score += 1
        
        # Check for breached limits
        for limit in self.risk_limits.values():
            if limit.is_breached:
                if limit.severity == RiskLevel.CRITICAL:
                    risk_score += 4
                elif limit.severity == RiskLevel.HIGH:
                    risk_score += 3
                elif limit.severity == RiskLevel.MEDIUM:
                    risk_score += 2
        
        # Determine risk level
        if risk_score >= 6:
            return RiskLevel.CRITICAL
        elif risk_score >= 4:
            return RiskLevel.HIGH
        elif risk_score >= 2:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    async def _update_risk_limits(self, daily_pnl_pct: float):
        """Update risk limit status."""
        # Daily loss limit
        self.risk_limits['max_daily_loss'].current_value = abs(daily_pnl_pct)
        self.risk_limits['max_daily_loss'].is_breached = daily_pnl_pct < -self.risk_limits['max_daily_loss'].threshold
        
        # Total exposure limit
        exposure_pct = (self.total_exposure / self.portfolio_value * 100) if self.portfolio_value > 0 else 0
        self.risk_limits['max_total_exposure'].current_value = exposure_pct
        self.risk_limits['max_total_exposure'].is_breached = exposure_pct > self.risk_limits['max_total_exposure'].threshold
    
    def check_position_risk(self, symbol: str, position_size: float, current_price: float) -> bool:
        """Check if a position meets risk requirements."""
        try:
            # Calculate position value
            position_value = abs(position_size * current_price)
            
            # Check individual position size limit
            if position_value > config.max_position_size:
                logger.warning(f"Position size limit exceeded for {symbol}: {position_value}")
                return False
            
            # Check portfolio concentration
            if self.portfolio_value > 0:
                concentration = position_value / self.portfolio_value
                if concentration > 0.2:  # Max 20% in single position
                    logger.warning(f"Portfolio concentration limit exceeded for {symbol}: {concentration:.2%}")
                    return False
            
            # Check total exposure
            new_total_exposure = self.total_exposure + position_value
            if self.portfolio_value > 0:
                exposure_ratio = new_total_exposure / self.portfolio_value
                if exposure_ratio > 0.8:  # Max 80% total exposure
                    logger.warning(f"Total exposure limit would be exceeded: {exposure_ratio:.2%}")
                    return False
            
            return True
            
        except Exception as e:
            log_error(f"Error checking position risk for {symbol}", e)
            return False
    
    def calculate_position_size(self, symbol: str, risk_amount: float, entry_price: float, 
                              stop_loss_price: Optional[float] = None) -> float:
        """Calculate optimal position size based on risk parameters."""
        try:
            if config.position_size_method == "fixed":
                return config.max_position_size / entry_price
            
            elif config.position_size_method == "kelly":
                # Kelly criterion (simplified)
                win_rate = 0.6  # Assume 60% win rate
                avg_win = 0.02  # 2% average win
                avg_loss = 0.01  # 1% average loss
                
                kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
                kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
                
                return (self.portfolio_value * kelly_fraction) / entry_price
            
            elif config.position_size_method == "volatility":
                # Volatility-based sizing
                prices = self.market_data_manager.get_price_history(symbol, periods=30)
                if len(prices) > 10:
                    returns = np.diff(np.log(prices))
                    volatility = np.std(returns) * np.sqrt(252)  # Annualized
                    
                    # Inverse volatility sizing
                    vol_factor = 0.2 / max(volatility, 0.1)  # Target 20% volatility
                    return (self.portfolio_value * vol_factor * config.risk_per_trade) / entry_price
            
            # Default to fixed sizing
            return config.max_position_size / entry_price
            
        except Exception as e:
            log_error(f"Error calculating position size for {symbol}", e)
            return config.max_position_size / entry_price
    
    def should_halt_trading(self) -> bool:
        """Check if trading should be halted due to risk conditions."""
        # Check critical risk level
        if self.current_risk_level == RiskLevel.CRITICAL:
            logger.critical("Trading halted due to critical risk level")
            return True
        
        # Check daily loss limit
        if self.risk_limits['max_daily_loss'].is_breached:
            logger.critical("Trading halted due to daily loss limit breach")
            return True
        
        # Check if portfolio value has dropped significantly
        if len(self.portfolio_history) > 1:
            portfolio_decline = (self.portfolio_history[0] - self.portfolio_value) / self.portfolio_history[0]
            if portfolio_decline > 0.2:  # 20% total decline
                logger.critical("Trading halted due to significant portfolio decline")
                return True
        
        return False
    
    def get_risk_summary(self) -> Dict:
        """Get comprehensive risk summary."""
        breached_limits = [limit for limit in self.risk_limits.values() if limit.is_breached]
        
        return {
            'risk_level': self.current_risk_level.value,
            'portfolio_value': self.portfolio_value,
            'total_exposure': self.total_exposure,
            'exposure_ratio': (self.total_exposure / self.portfolio_value) if self.portfolio_value > 0 else 0,
            'daily_pnl': self._calculate_daily_pnl(),
            'max_drawdown': self._calculate_max_drawdown(),
            'var_95': self._calculate_var_95(),
            'sharpe_ratio': self._calculate_sharpe_ratio(),
            'breached_limits': len(breached_limits),
            'should_halt_trading': self.should_halt_trading(),
            'last_update': self.last_risk_check
        }
    
    def add_position_exposure(self, symbol: str, position_value: float):
        """Add position to total exposure tracking."""
        self.total_exposure += abs(position_value)
        logger.debug(f"Added exposure for {symbol}: {position_value}, Total: {self.total_exposure}")
    
    def remove_position_exposure(self, symbol: str, position_value: float):
        """Remove position from total exposure tracking."""
        self.total_exposure = max(0, self.total_exposure - abs(position_value))
        logger.debug(f"Removed exposure for {symbol}: {position_value}, Total: {self.total_exposure}")
