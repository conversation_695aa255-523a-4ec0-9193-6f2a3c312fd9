"""
ArbTrader - Professional Cryptocurrency Arbitrage Trading System

A comprehensive trading system implementing:
- Triangular Arbitrage
- Statistical Arbitrage (Pairs Trading)
- Cash-and-Carry (Basis Trading)

Author: ArbTrader Team
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "ArbTrader Team"

from .core.config import Config
from .core.exchange import ExchangeManager
from .core.portfolio import PortfolioManager

__all__ = ["Config", "ExchangeManager", "PortfolioManager"]
