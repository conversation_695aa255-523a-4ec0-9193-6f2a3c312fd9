"""Command-line interface for ArbTrader."""

import asyncio
import click
import sys
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live
from rich.layout import Layout
import time

from .core.portfolio import PortfolioManager
from .core.config import config
from .core.logger import get_logger

console = Console()
logger = get_logger(__name__)


@click.group()
@click.version_option(version="1.0.0")
def cli():
    """ArbTrader - Professional Cryptocurrency Arbitrage Trading System"""
    pass


@cli.command()
@click.option('--paper', is_flag=True, help='Run in paper trading mode')
@click.option('--live', is_flag=True, help='Run in live trading mode')
def start(paper, live):
    """Start the ArbTrader system."""
    
    if paper and live:
        console.print("[red]Error: Cannot specify both --paper and --live modes[/red]")
        sys.exit(1)
    
    if live:
        config.trading_mode = "live"
        console.print("[yellow]WARNING: Starting in LIVE trading mode![/yellow]")
        if not click.confirm("Are you sure you want to trade with real money?"):
            console.print("[red]Aborted[/red]")
            sys.exit(1)
    else:
        config.trading_mode = "paper"
        console.print("[green]Starting in paper trading mode[/green]")
    
    # Display configuration
    _display_config()
    
    # Start the trading system
    asyncio.run(_start_trading())


@cli.command()
def status():
    """Show current system status."""
    console.print("[blue]ArbTrader Status[/blue]")
    
    # This would connect to a running instance to get status
    # For now, show configuration
    _display_config()


@cli.command()
def config_check():
    """Check configuration and API connectivity."""
    console.print("[blue]Checking ArbTrader Configuration...[/blue]")
    
    # Check configuration
    try:
        console.print(f"✓ Trading mode: {config.trading_mode}")
        console.print(f"✓ Max position size: ${config.max_position_size}")
        console.print(f"✓ Risk per trade: {config.risk_per_trade * 100}%")
        console.print(f"✓ Max daily loss: {config.max_daily_loss * 100}%")
        
        # Check enabled strategies
        strategies = []
        if config.enable_triangular_arbitrage:
            strategies.append("Triangular Arbitrage")
        if config.enable_pairs_trading:
            strategies.append("Pairs Trading")
        if config.enable_basis_trading:
            strategies.append("Basis Trading")
        
        console.print(f"✓ Enabled strategies: {', '.join(strategies)}")
        
        # Test API connectivity
        console.print("\n[blue]Testing API connectivity...[/blue]")
        asyncio.run(_test_api_connection())
        
    except Exception as e:
        console.print(f"[red]Configuration error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--symbol', help='Symbol to analyze (e.g., BTCUSDT)')
def analyze(symbol):
    """Analyze market data for arbitrage opportunities."""
    if not symbol:
        console.print("[red]Error: Symbol is required[/red]")
        sys.exit(1)
    
    console.print(f"[blue]Analyzing {symbol} for arbitrage opportunities...[/blue]")
    
    # This would perform analysis on the specified symbol
    console.print("[yellow]Analysis feature coming soon![/yellow]")


def _display_config():
    """Display current configuration."""
    table = Table(title="ArbTrader Configuration")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Trading Mode", config.trading_mode.upper())
    table.add_row("Testnet", "Yes" if config.binance_testnet else "No")
    table.add_row("Max Position Size", f"${config.max_position_size}")
    table.add_row("Risk Per Trade", f"{config.risk_per_trade * 100}%")
    table.add_row("Max Daily Loss", f"{config.max_daily_loss * 100}%")
    
    # Strategies
    strategies = []
    if config.enable_triangular_arbitrage:
        strategies.append("Triangular Arbitrage")
    if config.enable_pairs_trading:
        strategies.append("Pairs Trading")
    if config.enable_basis_trading:
        strategies.append("Basis Trading")
    
    table.add_row("Enabled Strategies", ", ".join(strategies))
    
    # Strategy settings
    table.add_row("Min Profit Threshold", f"{config.min_profit_threshold * 100}%")
    table.add_row("Correlation Threshold", f"{config.correlation_threshold}")
    table.add_row("Z-Score Entry", f"{config.zscore_entry}")
    table.add_row("Min Basis Spread", f"{config.min_basis_spread * 100}%")
    
    console.print(table)


async def _test_api_connection():
    """Test API connection."""
    try:
        from .core.exchange import ExchangeManager
        
        exchange = ExchangeManager()
        if await exchange.initialize():
            console.print("✓ Binance API connection successful")
            
            # Test getting account info
            balances = await exchange.get_account_balance()
            console.print(f"✓ Account has {len(balances)} assets with balance")
            
            await exchange.close()
        else:
            console.print("[red]✗ Failed to connect to Binance API[/red]")
            
    except Exception as e:
        console.print(f"[red]✗ API connection error: {e}[/red]")


async def _start_trading():
    """Start the trading system with live dashboard."""
    portfolio_manager = PortfolioManager()
    
    try:
        # Initialize
        console.print("[blue]Initializing ArbTrader...[/blue]")
        if not await portfolio_manager.initialize():
            console.print("[red]Failed to initialize ArbTrader[/red]")
            return
        
        console.print("[green]✓ ArbTrader initialized successfully[/green]")
        
        # Start trading with live dashboard
        await _run_with_dashboard(portfolio_manager)
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Shutdown requested by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        logger.error(f"CLI error: {e}")
    finally:
        await portfolio_manager.stop_trading()


async def _run_with_dashboard(portfolio_manager: PortfolioManager):
    """Run trading with live dashboard."""
    
    def create_dashboard():
        """Create the dashboard layout."""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )
        
        layout["main"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        # Header
        layout["header"].update(
            Panel("[bold blue]ArbTrader - Professional Cryptocurrency Arbitrage System[/bold blue]", 
                  style="blue")
        )
        
        # Footer
        layout["footer"].update(
            Panel("[dim]Press Ctrl+C to stop trading[/dim]", style="dim")
        )
        
        return layout
    
    def update_dashboard():
        """Update dashboard content."""
        layout = create_dashboard()
        
        # Get current status
        try:
            # This would be async in real implementation
            status_table = Table(title="Portfolio Status")
            status_table.add_column("Metric", style="cyan")
            status_table.add_column("Value", style="green")
            
            status_table.add_row("Status", "Running" if portfolio_manager.is_running else "Stopped")
            status_table.add_row("Trading Mode", config.trading_mode.upper())
            status_table.add_row("Uptime", f"{(time.time() - portfolio_manager.start_time)/3600:.1f}h" if portfolio_manager.start_time > 0 else "0h")
            status_table.add_row("Active Strategies", str(portfolio_manager.strategies_active))
            
            layout["left"].update(Panel(status_table, title="Status"))
            
            # Strategy performance
            perf_table = Table(title="Strategy Performance")
            perf_table.add_column("Strategy", style="cyan")
            perf_table.add_column("Signals", style="yellow")
            perf_table.add_column("Trades", style="green")
            perf_table.add_column("Profit", style="green")
            
            if config.enable_triangular_arbitrage:
                stats = portfolio_manager.triangular_arbitrage.get_statistics()
                perf_table.add_row("Triangular Arbitrage", 
                                 str(stats['opportunities_found']),
                                 str(stats['opportunities_executed']),
                                 f"${stats['total_profit']:.4f}")
            
            if config.enable_pairs_trading:
                stats = portfolio_manager.pairs_trading.get_statistics()
                perf_table.add_row("Pairs Trading",
                                 str(stats['signals_generated']),
                                 str(stats['trades_executed']),
                                 f"${stats['total_profit']:.4f}")
            
            if config.enable_basis_trading:
                stats = portfolio_manager.basis_trading.get_statistics()
                perf_table.add_row("Basis Trading",
                                 str(stats['opportunities_found']),
                                 str(stats['trades_executed']),
                                 f"${stats['total_profit']:.4f}")
            
            layout["right"].update(Panel(perf_table, title="Performance"))
            
        except Exception as e:
            layout["left"].update(Panel(f"[red]Error updating dashboard: {e}[/red]"))
        
        return layout
    
    # Start trading in background
    trading_task = asyncio.create_task(portfolio_manager.start_trading())
    
    # Run live dashboard
    try:
        with Live(update_dashboard(), refresh_per_second=1, screen=True) as live:
            while portfolio_manager.is_running:
                live.update(update_dashboard())
                await asyncio.sleep(1)
                
                # Check if trading task completed
                if trading_task.done():
                    break
                    
    except KeyboardInterrupt:
        console.print("\n[yellow]Stopping ArbTrader...[/yellow]")
        await portfolio_manager.stop_trading()
        trading_task.cancel()
        
        try:
            await trading_task
        except asyncio.CancelledError:
            pass


def main():
    """Main entry point."""
    cli()


if __name__ == "__main__":
    main()
