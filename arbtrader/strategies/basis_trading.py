"""Cash-and-carry (basis trading) strategy implementation."""

import asyncio
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from ..core.exchange import ExchangeManager, Trade
from ..core.config import config
from ..core.logger import get_logger, log_trade, log_error
from ..data.market_data import MarketDataManager, TickerData

logger = get_logger(__name__)


@dataclass
class FuturesContract:
    """Futures contract information."""
    symbol: str
    underlying: str
    expiry_date: datetime
    contract_size: float
    tick_size: float
    funding_rate: float
    funding_time: datetime


@dataclass
class BasisOpportunity:
    """Basis trading opportunity."""
    spot_symbol: str
    futures_symbol: str
    spot_price: float
    futures_price: float
    basis: float  # futures_price - spot_price
    basis_percentage: float
    annualized_return: float
    funding_rate: float
    time_to_expiry: float  # in days
    opportunity_type: str  # 'contango', 'backwardation'
    timestamp: float


@dataclass
class BasisPosition:
    """Active basis trading position."""
    spot_symbol: str
    futures_symbol: str
    position_type: str  # 'long_basis', 'short_basis'
    spot_quantity: float
    futures_quantity: float
    spot_entry_price: float
    futures_entry_price: float
    entry_basis: float
    entry_time: float
    target_profit: float
    stop_loss: float
    unrealized_pnl: float = 0.0
    
    def __str__(self):
        return f"{self.position_type.upper()} {self.spot_symbol}/{self.futures_symbol} (basis: {self.entry_basis:.4f})"


class BasisTradingEngine:
    """Cash-and-carry (basis trading) engine."""
    
    def __init__(self, exchange_manager: ExchangeManager, market_data_manager: MarketDataManager):
        self.exchange_manager = exchange_manager
        self.market_data_manager = market_data_manager
        
        # Futures contracts and opportunities
        self.futures_contracts: Dict[str, FuturesContract] = {}
        self.active_positions: Dict[str, BasisPosition] = {}
        
        # Performance tracking
        self.opportunities_found = 0
        self.trades_executed = 0
        self.total_profit = 0.0
        
        self.is_running = False
        self.last_scan_time = 0
    
    async def initialize(self):
        """Initialize the basis trading engine."""
        try:
            # Load futures contracts information
            await self._load_futures_contracts()
            logger.info(f"Loaded {len(self.futures_contracts)} futures contracts")
            return True
            
        except Exception as e:
            log_error("Failed to initialize basis trading engine", e)
            return False
    
    async def _load_futures_contracts(self):
        """Load available futures contracts from Binance."""
        try:
            # Get futures exchange info
            # Note: This would require Binance Futures API integration
            # For now, we'll use common perpetual futures
            
            common_perpetuals = [
                'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOTUSDT',
                'LINKUSDT', 'LTCUSDT', 'BCHUSDT', 'XLMUSDT', 'EOSUSDT'
            ]
            
            for symbol in common_perpetuals:
                # Create perpetual contract (no expiry)
                contract = FuturesContract(
                    symbol=symbol,
                    underlying=symbol.replace('USDT', ''),
                    expiry_date=datetime.max,  # Perpetual
                    contract_size=1.0,
                    tick_size=0.01,
                    funding_rate=0.0001,  # Default 0.01%
                    funding_time=datetime.now()
                )
                
                self.futures_contracts[symbol] = contract
            
            logger.info("Loaded perpetual futures contracts")
            
        except Exception as e:
            log_error("Error loading futures contracts", e)
    
    async def start_monitoring(self):
        """Start monitoring for basis trading opportunities."""
        if not self.futures_contracts:
            logger.error("No futures contracts available")
            return
        
        self.is_running = True
        logger.info("Starting basis trading monitoring")
        
        while self.is_running:
            try:
                await self._scan_basis_opportunities()
                await self._manage_positions()
                await asyncio.sleep(10)  # Scan every 10 seconds
                
            except Exception as e:
                log_error("Error in basis trading monitoring", e)
                await asyncio.sleep(30)
    
    async def _scan_basis_opportunities(self):
        """Scan for basis trading opportunities."""
        start_time = time.time()
        
        for futures_symbol, contract in self.futures_contracts.items():
            try:
                # Get corresponding spot symbol
                spot_symbol = futures_symbol  # For perpetuals, same symbol
                
                opportunity = await self._calculate_basis_opportunity(spot_symbol, futures_symbol, contract)
                
                if opportunity and self._is_profitable_opportunity(opportunity):
                    self.opportunities_found += 1
                    logger.info(f"Basis opportunity: {opportunity.opportunity_type} "
                              f"{opportunity.spot_symbol} basis: {opportunity.basis_percentage:.4f}% "
                              f"annualized: {opportunity.annualized_return:.2f}%")
                    
                    if await self._should_execute_opportunity(opportunity):
                        await self._execute_basis_trade(opportunity)
                        
            except Exception as e:
                log_error(f"Error scanning basis for {futures_symbol}", e)
        
        self.last_scan_time = time.time() - start_time
    
    async def _calculate_basis_opportunity(self, spot_symbol: str, futures_symbol: str, 
                                         contract: FuturesContract) -> Optional[BasisOpportunity]:
        """Calculate basis trading opportunity."""
        try:
            # Get spot price
            spot_ticker = self.market_data_manager.get_ticker(spot_symbol)
            if not spot_ticker:
                # Try to get from exchange directly
                spot_price = await self.exchange_manager.get_ticker_price(spot_symbol)
                if not spot_price:
                    return None
            else:
                spot_price = spot_ticker.price
            
            # Get futures price (for perpetuals, this is the same as spot in most cases)
            # In a real implementation, you'd connect to Binance Futures API
            futures_price = spot_price * (1 + contract.funding_rate)  # Simplified
            
            # Calculate basis
            basis = futures_price - spot_price
            basis_percentage = (basis / spot_price) * 100
            
            # Calculate annualized return
            # For perpetuals, use funding period (8 hours = 3 times per day)
            funding_periods_per_year = 365 * 3  # 3 funding periods per day
            annualized_return = basis_percentage * funding_periods_per_year
            
            # Determine opportunity type
            opportunity_type = 'contango' if basis > 0 else 'backwardation'
            
            return BasisOpportunity(
                spot_symbol=spot_symbol,
                futures_symbol=futures_symbol,
                spot_price=spot_price,
                futures_price=futures_price,
                basis=basis,
                basis_percentage=basis_percentage,
                annualized_return=annualized_return,
                funding_rate=contract.funding_rate,
                time_to_expiry=float('inf'),  # Perpetual
                opportunity_type=opportunity_type,
                timestamp=time.time()
            )
            
        except Exception as e:
            log_error(f"Error calculating basis opportunity for {spot_symbol}/{futures_symbol}", e)
            return None
    
    def _is_profitable_opportunity(self, opportunity: BasisOpportunity) -> bool:
        """Check if basis opportunity is profitable."""
        # Check minimum basis spread
        if abs(opportunity.basis_percentage) < config.min_basis_spread * 100:
            return False
        
        # Check funding rate threshold
        if abs(opportunity.funding_rate) < config.funding_rate_threshold:
            return False
        
        # Check annualized return threshold (should be > 10% annually)
        if abs(opportunity.annualized_return) < 10.0:
            return False
        
        return True
    
    async def _should_execute_opportunity(self, opportunity: BasisOpportunity) -> bool:
        """Determine if basis opportunity should be executed."""
        # Check if we already have a position in this pair
        position_key = f"{opportunity.spot_symbol}_{opportunity.futures_symbol}"
        if position_key in self.active_positions:
            return False
        
        # Check maximum number of positions
        if len(self.active_positions) >= 3:  # Max 3 basis positions
            return False
        
        # Check available balance
        # This would require checking actual account balances
        
        return True
    
    async def _execute_basis_trade(self, opportunity: BasisOpportunity):
        """Execute a basis trading opportunity."""
        try:
            logger.info(f"Executing basis trade: {opportunity.opportunity_type} "
                       f"{opportunity.spot_symbol}/{opportunity.futures_symbol}")
            
            # Calculate position size
            position_size = self._calculate_position_size(opportunity)
            
            if opportunity.opportunity_type == 'contango':
                # Futures price > Spot price
                # Strategy: Buy spot, sell futures (collect basis)
                spot_action = 'buy'
                futures_action = 'sell'
                position_type = 'long_basis'
                
            else:  # backwardation
                # Futures price < Spot price
                # Strategy: Sell spot, buy futures
                spot_action = 'sell'
                futures_action = 'buy'
                position_type = 'short_basis'
            
            # Execute spot trade
            spot_trade = await self.exchange_manager.place_market_order(
                opportunity.spot_symbol, spot_action, position_size
            )
            
            if not spot_trade:
                logger.error(f"Failed to execute spot trade for {opportunity.spot_symbol}")
                return False
            
            # For demonstration, we'll simulate the futures trade
            # In production, this would use Binance Futures API
            futures_trade = Trade(
                symbol=opportunity.futures_symbol,
                side=futures_action,
                quantity=position_size,
                price=opportunity.futures_price,
                fee=position_size * opportunity.futures_price * 0.0004,  # 0.04% futures fee
                timestamp=time.time(),
                order_id=f"FUTURES_{int(time.time() * 1000)}"
            )
            
            # Create position record
            position = BasisPosition(
                spot_symbol=opportunity.spot_symbol,
                futures_symbol=opportunity.futures_symbol,
                position_type=position_type,
                spot_quantity=spot_trade.quantity if spot_action == 'buy' else -spot_trade.quantity,
                futures_quantity=futures_trade.quantity if futures_action == 'buy' else -futures_trade.quantity,
                spot_entry_price=spot_trade.price,
                futures_entry_price=futures_trade.price,
                entry_basis=opportunity.basis,
                entry_time=time.time(),
                target_profit=abs(opportunity.basis) * 0.8,  # Target 80% of basis
                stop_loss=abs(opportunity.basis) * 1.5  # Stop at 150% of basis
            )
            
            position_key = f"{opportunity.spot_symbol}_{opportunity.futures_symbol}"
            self.active_positions[position_key] = position
            self.trades_executed += 1
            
            log_trade(f"Basis position opened: {position}")
            return True
            
        except Exception as e:
            log_error(f"Error executing basis trade", e)
            return False
    
    def _calculate_position_size(self, opportunity: BasisOpportunity) -> float:
        """Calculate position size for basis trade."""
        # Use a fraction of max position size
        base_size = config.max_position_size * 0.2  # 20% of max position
        
        # Adjust based on basis strength
        basis_multiplier = min(abs(opportunity.basis_percentage) / config.min_basis_spread / 100, 2.0)
        
        position_size = base_size * basis_multiplier
        return position_size
    
    async def _manage_positions(self):
        """Manage active basis positions."""
        for position_key, position in list(self.active_positions.items()):
            try:
                # Update unrealized P&L
                await self._update_position_pnl(position)
                
                # Check exit conditions
                if await self._should_close_position(position):
                    await self._close_basis_position(position)
                    del self.active_positions[position_key]
                    
            except Exception as e:
                log_error(f"Error managing position {position_key}", e)
    
    async def _update_position_pnl(self, position: BasisPosition):
        """Update unrealized P&L for a position."""
        try:
            # Get current prices
            spot_ticker = self.market_data_manager.get_ticker(position.spot_symbol)
            current_spot_price = spot_ticker.price if spot_ticker else position.spot_entry_price
            
            # For futures, we'd get from futures API. For now, estimate
            current_futures_price = current_spot_price * (1 + self.futures_contracts[position.futures_symbol].funding_rate)
            
            # Calculate P&L for each leg
            spot_pnl = position.spot_quantity * (current_spot_price - position.spot_entry_price)
            futures_pnl = position.futures_quantity * (current_futures_price - position.futures_entry_price)
            
            position.unrealized_pnl = spot_pnl + futures_pnl
            
        except Exception as e:
            log_error(f"Error updating P&L for position {position}", e)
    
    async def _should_close_position(self, position: BasisPosition) -> bool:
        """Check if position should be closed."""
        # Check profit target
        if position.unrealized_pnl >= position.target_profit:
            logger.info(f"Profit target reached for {position}")
            return True
        
        # Check stop loss
        if position.unrealized_pnl <= -position.stop_loss:
            logger.info(f"Stop loss triggered for {position}")
            return True
        
        # Check maximum holding period (7 days for perpetuals)
        holding_time = time.time() - position.entry_time
        if holding_time > 7 * 24 * 3600:
            logger.info(f"Maximum holding period reached for {position}")
            return True
        
        # Check if basis has converged significantly
        current_spot_ticker = self.market_data_manager.get_ticker(position.spot_symbol)
        if current_spot_ticker:
            current_futures_price = current_spot_ticker.price * (1 + self.futures_contracts[position.futures_symbol].funding_rate)
            current_basis = current_futures_price - current_spot_ticker.price
            
            # Close if basis has converged by 70%
            basis_convergence = abs(current_basis) / abs(position.entry_basis)
            if basis_convergence < 0.3:
                logger.info(f"Basis converged for {position}")
                return True
        
        return False
    
    async def _close_basis_position(self, position: BasisPosition):
        """Close a basis trading position."""
        try:
            logger.info(f"Closing basis position: {position}")
            
            # Determine closing actions (opposite of opening)
            if position.spot_quantity > 0:
                spot_action = 'sell'
            else:
                spot_action = 'buy'
            
            if position.futures_quantity > 0:
                futures_action = 'sell'
            else:
                futures_action = 'buy'
            
            # Close spot position
            spot_trade = await self.exchange_manager.place_market_order(
                position.spot_symbol, spot_action, abs(position.spot_quantity)
            )
            
            if spot_trade:
                # Simulate futures close
                futures_trade = Trade(
                    symbol=position.futures_symbol,
                    side=futures_action,
                    quantity=abs(position.futures_quantity),
                    price=spot_trade.price * (1 + self.futures_contracts[position.futures_symbol].funding_rate),
                    fee=abs(position.futures_quantity) * spot_trade.price * 0.0004,
                    timestamp=time.time(),
                    order_id=f"FUTURES_CLOSE_{int(time.time() * 1000)}"
                )
                
                # Calculate final P&L
                final_pnl = self._calculate_final_pnl(position, spot_trade.price, futures_trade.price)
                self.total_profit += final_pnl
                
                log_trade(f"Basis position closed: {position} | P&L: {final_pnl:.4f}")
                
        except Exception as e:
            log_error(f"Error closing basis position {position}", e)
    
    def _calculate_final_pnl(self, position: BasisPosition, exit_spot_price: float, exit_futures_price: float) -> float:
        """Calculate final P&L for closed position."""
        spot_pnl = position.spot_quantity * (exit_spot_price - position.spot_entry_price)
        futures_pnl = position.futures_quantity * (exit_futures_price - position.futures_entry_price)
        return spot_pnl + futures_pnl
    
    def stop_monitoring(self):
        """Stop basis trading monitoring."""
        self.is_running = False
        logger.info("Basis trading monitoring stopped")
    
    def get_statistics(self) -> Dict:
        """Get basis trading statistics."""
        return {
            'futures_contracts': len(self.futures_contracts),
            'active_positions': len(self.active_positions),
            'opportunities_found': self.opportunities_found,
            'trades_executed': self.trades_executed,
            'total_profit': self.total_profit,
            'last_scan_time': self.last_scan_time
        }
