"""Statistical arbitrage (pairs trading) strategy implementation."""

import asyncio
import time
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.linear_model import LinearRegression
import warnings
warnings.filterwarnings('ignore')

from ..core.exchange import ExchangeManager, Trade
from ..core.config import config
from ..core.logger import get_logger, log_trade, log_error
from ..data.market_data import MarketDataManager, TickerData

logger = get_logger(__name__)


@dataclass
class TradingPair:
    """Trading pair for statistical arbitrage."""
    symbol1: str
    symbol2: str
    correlation: float
    beta: float  # Hedge ratio
    spread_mean: float
    spread_std: float
    last_update: float
    
    def __str__(self):
        return f"{self.symbol1}/{self.symbol2} (corr: {self.correlation:.3f})"


@dataclass
class PairsSignal:
    """Pairs trading signal."""
    pair: TradingPair
    signal_type: str  # 'entry_long', 'entry_short', 'exit', 'none'
    z_score: float
    spread: float
    confidence: float
    timestamp: float
    
    # Trade details
    symbol1_action: str  # 'buy', 'sell', 'hold'
    symbol2_action: str  # 'buy', 'sell', 'hold'
    position_size: float


@dataclass
class PairsPosition:
    """Active pairs trading position."""
    pair: TradingPair
    entry_time: float
    entry_z_score: float
    position_type: str  # 'long', 'short'
    symbol1_quantity: float
    symbol2_quantity: float
    symbol1_entry_price: float
    symbol2_entry_price: float
    unrealized_pnl: float = 0.0
    
    def __str__(self):
        return f"{self.position_type.upper()} {self.pair} (z: {self.entry_z_score:.2f})"


class PairsTradingEngine:
    """Statistical arbitrage (pairs trading) engine."""
    
    def __init__(self, exchange_manager: ExchangeManager, market_data_manager: MarketDataManager):
        self.exchange_manager = exchange_manager
        self.market_data_manager = market_data_manager
        
        # Trading pairs and positions
        self.trading_pairs: List[TradingPair] = []
        self.active_positions: Dict[str, PairsPosition] = {}
        
        # Performance tracking
        self.signals_generated = 0
        self.trades_executed = 0
        self.total_profit = 0.0
        self.win_rate = 0.0
        
        self.is_running = False
        self.last_analysis_time = 0
    
    async def initialize(self, symbols: List[str]):
        """Initialize the pairs trading engine."""
        try:
            # Find correlated pairs
            await self._find_correlated_pairs(symbols)
            logger.info(f"Found {len(self.trading_pairs)} correlated pairs for trading")
            return True
            
        except Exception as e:
            log_error("Failed to initialize pairs trading engine", e)
            return False
    
    async def _find_correlated_pairs(self, symbols: List[str]):
        """Find correlated trading pairs from available symbols."""
        logger.info("Analyzing correlations between trading pairs...")
        
        # Filter symbols to major cryptocurrencies for better liquidity
        major_symbols = [s for s in symbols if any(base in s for base in config.base_currencies)]
        major_symbols = major_symbols[:50]  # Limit to top 50 for performance
        
        # Get historical price data for correlation analysis
        price_data = {}
        for symbol in major_symbols:
            try:
                # Get price history
                prices = self.market_data_manager.get_price_history(symbol, periods=config.lookback_period * 24 * 60)  # Daily data
                if len(prices) >= config.lookback_period:
                    # Convert to daily returns
                    prices_array = np.array(prices)
                    returns = np.diff(np.log(prices_array))
                    if len(returns) > 0:
                        price_data[symbol] = returns
            except Exception as e:
                log_error(f"Error getting price data for {symbol}", e)
        
        # Calculate correlations between all pairs
        symbols_with_data = list(price_data.keys())
        
        for i, symbol1 in enumerate(symbols_with_data):
            for j, symbol2 in enumerate(symbols_with_data[i+1:], i+1):
                try:
                    # Ensure we have enough data points
                    min_length = min(len(price_data[symbol1]), len(price_data[symbol2]))
                    if min_length < config.lookback_period:
                        continue
                    
                    # Align data lengths
                    returns1 = price_data[symbol1][-min_length:]
                    returns2 = price_data[symbol2][-min_length:]
                    
                    # Calculate correlation
                    correlation, p_value = stats.pearsonr(returns1, returns2)
                    
                    # Check if correlation meets threshold and is statistically significant
                    if (abs(correlation) >= config.correlation_threshold and 
                        p_value < 0.05 and 
                        not np.isnan(correlation)):
                        
                        # Calculate hedge ratio using linear regression
                        X = returns1.reshape(-1, 1)
                        y = returns2
                        
                        reg = LinearRegression().fit(X, y)
                        beta = reg.coef_[0]
                        
                        # Calculate spread statistics
                        spread = returns2 - beta * returns1
                        spread_mean = np.mean(spread)
                        spread_std = np.std(spread)
                        
                        if spread_std > 0:  # Avoid division by zero
                            trading_pair = TradingPair(
                                symbol1=symbol1,
                                symbol2=symbol2,
                                correlation=correlation,
                                beta=beta,
                                spread_mean=spread_mean,
                                spread_std=spread_std,
                                last_update=time.time()
                            )
                            
                            self.trading_pairs.append(trading_pair)
                            logger.debug(f"Found pair: {trading_pair}")
                
                except Exception as e:
                    log_error(f"Error analyzing pair {symbol1}/{symbol2}", e)
        
        # Sort pairs by correlation strength
        self.trading_pairs.sort(key=lambda x: abs(x.correlation), reverse=True)
        
        # Keep only top pairs to avoid overtrading
        self.trading_pairs = self.trading_pairs[:20]
    
    async def start_monitoring(self):
        """Start monitoring for pairs trading opportunities."""
        if not self.trading_pairs:
            logger.error("No trading pairs available")
            return
        
        self.is_running = True
        logger.info("Starting pairs trading monitoring")
        
        while self.is_running:
            try:
                await self._analyze_pairs()
                await self._manage_positions()
                await asyncio.sleep(5)  # Analyze every 5 seconds
                
            except Exception as e:
                log_error("Error in pairs trading monitoring", e)
                await asyncio.sleep(10)
    
    async def _analyze_pairs(self):
        """Analyze all trading pairs for signals."""
        start_time = time.time()
        
        for pair in self.trading_pairs:
            try:
                signal = await self._generate_signal(pair)
                if signal and signal.signal_type != 'none':
                    self.signals_generated += 1
                    logger.info(f"Pairs signal: {signal.signal_type} for {pair} (z: {signal.z_score:.2f})")
                    
                    # Execute signal if conditions are met
                    if await self._should_execute_signal(signal):
                        await self._execute_pairs_trade(signal)
                        
            except Exception as e:
                log_error(f"Error analyzing pair {pair}", e)
        
        self.last_analysis_time = time.time() - start_time
    
    async def _generate_signal(self, pair: TradingPair) -> Optional[PairsSignal]:
        """Generate trading signal for a pair."""
        try:
            # Get current prices
            ticker1 = self.market_data_manager.get_ticker(pair.symbol1)
            ticker2 = self.market_data_manager.get_ticker(pair.symbol2)
            
            if not ticker1 or not ticker2:
                return None
            
            # Get recent price history for spread calculation
            prices1 = self.market_data_manager.get_price_history(pair.symbol1, periods=100)
            prices2 = self.market_data_manager.get_price_history(pair.symbol2, periods=100)
            
            if len(prices1) < 50 or len(prices2) < 50:
                return None
            
            # Calculate current spread
            min_length = min(len(prices1), len(prices2))
            prices1 = np.array(prices1[-min_length:])
            prices2 = np.array(prices2[-min_length:])
            
            # Calculate log returns
            returns1 = np.diff(np.log(prices1))
            returns2 = np.diff(np.log(prices2))
            
            # Current spread
            current_spread = returns2[-1] - pair.beta * returns1[-1]
            
            # Calculate z-score
            z_score = (current_spread - pair.spread_mean) / pair.spread_std
            
            # Generate signal based on z-score
            signal_type = 'none'
            symbol1_action = 'hold'
            symbol2_action = 'hold'
            confidence = abs(z_score) / config.zscore_entry
            
            pair_key = f"{pair.symbol1}_{pair.symbol2}"
            
            # Entry signals
            if abs(z_score) >= config.zscore_entry:
                if pair_key not in self.active_positions:
                    if z_score > 0:  # Spread is high, expect mean reversion
                        signal_type = 'entry_short'
                        symbol1_action = 'buy'   # Buy underperforming asset
                        symbol2_action = 'sell'  # Sell overperforming asset
                    else:  # Spread is low, expect mean reversion
                        signal_type = 'entry_long'
                        symbol1_action = 'sell'  # Sell overperforming asset
                        symbol2_action = 'buy'   # Buy underperforming asset
            
            # Exit signals
            elif abs(z_score) <= config.zscore_exit:
                if pair_key in self.active_positions:
                    signal_type = 'exit'
                    position = self.active_positions[pair_key]
                    if position.position_type == 'long':
                        symbol1_action = 'buy'   # Close short position
                        symbol2_action = 'sell'  # Close long position
                    else:
                        symbol1_action = 'sell'  # Close long position
                        symbol2_action = 'buy'   # Close short position
            
            if signal_type != 'none':
                return PairsSignal(
                    pair=pair,
                    signal_type=signal_type,
                    z_score=z_score,
                    spread=current_spread,
                    confidence=confidence,
                    timestamp=time.time(),
                    symbol1_action=symbol1_action,
                    symbol2_action=symbol2_action,
                    position_size=self._calculate_position_size(pair)
                )
            
            return None
            
        except Exception as e:
            log_error(f"Error generating signal for {pair}", e)
            return None
    
    def _calculate_position_size(self, pair: TradingPair) -> float:
        """Calculate position size for a pairs trade."""
        # Simple fixed position sizing for now
        # In production, this should consider portfolio size, volatility, etc.
        base_position_size = config.max_position_size * 0.1  # 10% of max position
        return base_position_size
    
    async def _should_execute_signal(self, signal: PairsSignal) -> bool:
        """Determine if a pairs trading signal should be executed."""
        # Check confidence threshold
        if signal.confidence < 1.0:  # Require at least 1x the entry threshold
            return False
        
        # Check if we already have too many positions
        if len(self.active_positions) >= 5:  # Max 5 concurrent pairs positions
            return False
        
        # Check for conflicting positions
        pair_key = f"{signal.pair.symbol1}_{signal.pair.symbol2}"
        if signal.signal_type.startswith('entry') and pair_key in self.active_positions:
            return False
        
        return True
    
    async def _execute_pairs_trade(self, signal: PairsSignal):
        """Execute a pairs trading signal."""
        try:
            logger.info(f"Executing pairs trade: {signal.signal_type} for {signal.pair}")
            
            pair_key = f"{signal.pair.symbol1}_{signal.pair.symbol2}"
            
            if signal.signal_type.startswith('entry'):
                # Calculate quantities based on hedge ratio
                symbol1_quantity = signal.position_size
                symbol2_quantity = signal.position_size * abs(signal.pair.beta)
                
                # Execute trades
                trade1 = await self.exchange_manager.place_market_order(
                    signal.pair.symbol1, signal.symbol1_action, symbol1_quantity
                )
                trade2 = await self.exchange_manager.place_market_order(
                    signal.pair.symbol2, signal.symbol2_action, symbol2_quantity
                )
                
                if trade1 and trade2:
                    # Create position record
                    position = PairsPosition(
                        pair=signal.pair,
                        entry_time=time.time(),
                        entry_z_score=signal.z_score,
                        position_type='long' if signal.signal_type == 'entry_long' else 'short',
                        symbol1_quantity=trade1.quantity if signal.symbol1_action == 'buy' else -trade1.quantity,
                        symbol2_quantity=trade2.quantity if signal.symbol2_action == 'buy' else -trade2.quantity,
                        symbol1_entry_price=trade1.price,
                        symbol2_entry_price=trade2.price
                    )
                    
                    self.active_positions[pair_key] = position
                    self.trades_executed += 1
                    
                    log_trade(f"Pairs position opened: {position}")
            
            elif signal.signal_type == 'exit':
                if pair_key in self.active_positions:
                    position = self.active_positions[pair_key]
                    
                    # Close position
                    trade1 = await self.exchange_manager.place_market_order(
                        signal.pair.symbol1, signal.symbol1_action, abs(position.symbol1_quantity)
                    )
                    trade2 = await self.exchange_manager.place_market_order(
                        signal.pair.symbol2, signal.symbol2_action, abs(position.symbol2_quantity)
                    )
                    
                    if trade1 and trade2:
                        # Calculate P&L
                        pnl = self._calculate_position_pnl(position, trade1.price, trade2.price)
                        self.total_profit += pnl
                        
                        log_trade(f"Pairs position closed: {position} | P&L: {pnl:.4f}")
                        
                        del self.active_positions[pair_key]
            
        except Exception as e:
            log_error(f"Error executing pairs trade for {signal.pair}", e)
    
    def _calculate_position_pnl(self, position: PairsPosition, exit_price1: float, exit_price2: float) -> float:
        """Calculate P&L for a closed pairs position."""
        # Calculate P&L for each leg
        pnl1 = position.symbol1_quantity * (exit_price1 - position.symbol1_entry_price)
        pnl2 = position.symbol2_quantity * (exit_price2 - position.symbol2_entry_price)
        
        total_pnl = pnl1 + pnl2
        return total_pnl
    
    async def _manage_positions(self):
        """Manage active positions (stop losses, profit targets, etc.)."""
        for pair_key, position in list(self.active_positions.items()):
            try:
                # Update unrealized P&L
                ticker1 = self.market_data_manager.get_ticker(position.pair.symbol1)
                ticker2 = self.market_data_manager.get_ticker(position.pair.symbol2)
                
                if ticker1 and ticker2:
                    position.unrealized_pnl = self._calculate_position_pnl(
                        position, ticker1.price, ticker2.price
                    )
                
                # Check for stop loss or maximum holding period
                holding_time = time.time() - position.entry_time
                
                # Stop loss at -2% or holding for more than 24 hours
                if (position.unrealized_pnl < -config.max_position_size * 0.02 or 
                    holding_time > 24 * 3600):
                    
                    logger.info(f"Force closing position: {position} | P&L: {position.unrealized_pnl:.4f}")
                    
                    # Force close position
                    await self._force_close_position(position)
                    del self.active_positions[pair_key]
                    
            except Exception as e:
                log_error(f"Error managing position {pair_key}", e)
    
    async def _force_close_position(self, position: PairsPosition):
        """Force close a pairs position."""
        try:
            # Determine close actions (opposite of open actions)
            if position.symbol1_quantity > 0:
                symbol1_action = 'sell'
            else:
                symbol1_action = 'buy'
            
            if position.symbol2_quantity > 0:
                symbol2_action = 'sell'
            else:
                symbol2_action = 'buy'
            
            # Execute closing trades
            trade1 = await self.exchange_manager.place_market_order(
                position.pair.symbol1, symbol1_action, abs(position.symbol1_quantity)
            )
            trade2 = await self.exchange_manager.place_market_order(
                position.pair.symbol2, symbol2_action, abs(position.symbol2_quantity)
            )
            
            if trade1 and trade2:
                pnl = self._calculate_position_pnl(position, trade1.price, trade2.price)
                self.total_profit += pnl
                log_trade(f"Position force closed: {position} | P&L: {pnl:.4f}")
                
        except Exception as e:
            log_error(f"Error force closing position {position}", e)
    
    def stop_monitoring(self):
        """Stop pairs trading monitoring."""
        self.is_running = False
        logger.info("Pairs trading monitoring stopped")
    
    def get_statistics(self) -> Dict:
        """Get pairs trading statistics."""
        return {
            'trading_pairs': len(self.trading_pairs),
            'active_positions': len(self.active_positions),
            'signals_generated': self.signals_generated,
            'trades_executed': self.trades_executed,
            'total_profit': self.total_profit,
            'win_rate': self.win_rate,
            'last_analysis_time': self.last_analysis_time
        }
