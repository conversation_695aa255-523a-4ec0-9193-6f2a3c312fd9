"""Triangular arbitrage strategy implementation."""

import asyncio
import time
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from itertools import permutations
import math

from ..core.exchange import ExchangeManager, Trade
from ..core.config import config
from ..core.logger import get_logger, log_trade, log_error
from ..data.market_data import MarketDataManager, TickerData

logger = get_logger(__name__)


@dataclass
class ArbitrageOpportunity:
    """Triangular arbitrage opportunity data structure."""
    path: List[str]  # [symbol1, symbol2, symbol3]
    profit_percentage: float
    profit_amount: float
    base_amount: float
    execution_order: List[Tuple[str, str, float]]  # [(symbol, side, quantity), ...]
    timestamp: float
    fees_total: float


@dataclass
class TriangularPath:
    """Represents a triangular arbitrage path."""
    symbols: List[str]  # Three symbols forming the triangle
    base_currency: str
    intermediate_currency: str
    target_currency: str
    
    def __str__(self):
        return f"{self.base_currency} -> {self.intermediate_currency} -> {self.target_currency} -> {self.base_currency}"


class TriangularArbitrageEngine:
    """Triangular arbitrage detection and execution engine."""
    
    def __init__(self, exchange_manager: ExchangeManager, market_data_manager: MarketDataManager):
        self.exchange_manager = exchange_manager
        self.market_data_manager = market_data_manager
        self.triangular_paths: List[TriangularPath] = []
        self.opportunities_found = 0
        self.opportunities_executed = 0
        self.total_profit = 0.0
        self.is_running = False
        
        # Performance tracking
        self.last_scan_time = 0
        self.scan_count = 0
        
    async def initialize(self):
        """Initialize the triangular arbitrage engine."""
        try:
            # Generate all possible triangular paths
            await self._generate_triangular_paths()
            logger.info(f"Generated {len(self.triangular_paths)} triangular arbitrage paths")
            return True
            
        except Exception as e:
            log_error("Failed to initialize triangular arbitrage engine", e)
            return False
    
    async def _generate_triangular_paths(self):
        """Generate all possible triangular arbitrage paths."""
        # Get all available symbols
        symbols_info = self.exchange_manager.symbols_info
        
        # Filter active trading symbols
        active_symbols = [
            symbol for symbol, info in symbols_info.items()
            if info['status'] == 'TRADING' and 'SPOT' in info['permissions']
        ]
        
        # Extract unique currencies
        currencies = set()
        symbol_pairs = {}
        
        for symbol in active_symbols:
            info = symbols_info[symbol]
            base = info['baseAsset']
            quote = info['quoteAsset']
            currencies.add(base)
            currencies.add(quote)
            symbol_pairs[f"{base}{quote}"] = symbol
            symbol_pairs[f"{quote}{base}"] = symbol  # For reverse lookup
        
        # Focus on major currencies to reduce complexity
        major_currencies = set(config.base_currencies).intersection(currencies)
        
        # Generate triangular paths
        for base_currency in major_currencies:
            for intermediate_currency in major_currencies:
                if intermediate_currency == base_currency:
                    continue
                    
                for target_currency in major_currencies:
                    if target_currency in [base_currency, intermediate_currency]:
                        continue
                    
                    # Check if all required pairs exist
                    pair1 = f"{base_currency}{intermediate_currency}"
                    pair2 = f"{intermediate_currency}{target_currency}"
                    pair3 = f"{target_currency}{base_currency}"
                    
                    # Try different combinations
                    symbols = []
                    
                    # Path 1: BASE->INTER, INTER->TARGET, TARGET->BASE
                    if (pair1 in symbol_pairs and 
                        pair2 in symbol_pairs and 
                        pair3 in symbol_pairs):
                        symbols = [symbol_pairs[pair1], symbol_pairs[pair2], symbol_pairs[pair3]]
                    
                    # Path 2: BASE->INTER, TARGET->INTER, TARGET->BASE (reverse pair2)
                    elif (pair1 in symbol_pairs and 
                          f"{target_currency}{intermediate_currency}" in symbol_pairs and 
                          pair3 in symbol_pairs):
                        symbols = [
                            symbol_pairs[pair1], 
                            symbol_pairs[f"{target_currency}{intermediate_currency}"], 
                            symbol_pairs[pair3]
                        ]
                    
                    if symbols and len(set(symbols)) == 3:  # Ensure unique symbols
                        path = TriangularPath(
                            symbols=symbols,
                            base_currency=base_currency,
                            intermediate_currency=intermediate_currency,
                            target_currency=target_currency
                        )
                        self.triangular_paths.append(path)
    
    async def start_monitoring(self):
        """Start monitoring for triangular arbitrage opportunities."""
        if not self.triangular_paths:
            logger.error("No triangular paths available")
            return
        
        self.is_running = True
        logger.info("Starting triangular arbitrage monitoring")
        
        while self.is_running:
            try:
                await self._scan_opportunities()
                await asyncio.sleep(0.1)  # Scan every 100ms
                
            except Exception as e:
                log_error("Error in triangular arbitrage monitoring", e)
                await asyncio.sleep(1)
    
    async def _scan_opportunities(self):
        """Scan for triangular arbitrage opportunities."""
        start_time = time.time()
        
        for path in self.triangular_paths:
            try:
                opportunity = await self._calculate_arbitrage_opportunity(path)
                if opportunity and opportunity.profit_percentage > config.min_profit_threshold:
                    self.opportunities_found += 1
                    logger.info(f"Arbitrage opportunity found: {opportunity.profit_percentage:.4f}% profit")
                    
                    # Execute if profitable after fees and slippage
                    if await self._should_execute_opportunity(opportunity):
                        await self._execute_arbitrage(opportunity)
                        
            except Exception as e:
                log_error(f"Error scanning path {path}", e)
        
        # Update performance metrics
        self.scan_count += 1
        self.last_scan_time = time.time() - start_time
    
    async def _calculate_arbitrage_opportunity(self, path: TriangularPath) -> Optional[ArbitrageOpportunity]:
        """Calculate arbitrage opportunity for a triangular path."""
        try:
            # Get current prices for all symbols in the path
            prices = {}
            for symbol in path.symbols:
                ticker = self.market_data_manager.get_ticker(symbol)
                if not ticker:
                    return None
                prices[symbol] = ticker.price
            
            # Calculate the arbitrage opportunity
            base_amount = 100.0  # Start with 100 units of base currency
            current_amount = base_amount
            execution_order = []
            total_fees = 0.0
            
            # Execute the triangular path
            for i, symbol in enumerate(path.symbols):
                symbol_info = self.exchange_manager.get_symbol_info(symbol)
                if not symbol_info:
                    return None
                
                base_asset = symbol_info['baseAsset']
                quote_asset = symbol_info['quoteAsset']
                price = prices[symbol]
                
                # Determine trade direction based on path
                if i == 0:  # First trade: base -> intermediate
                    if base_asset == path.base_currency:
                        # Buy intermediate with base
                        side = 'buy'
                        quantity = current_amount / price
                        current_amount = quantity
                    else:
                        # Sell base for intermediate
                        side = 'sell'
                        quantity = current_amount
                        current_amount = quantity * price
                        
                elif i == 1:  # Second trade: intermediate -> target
                    if base_asset == path.intermediate_currency:
                        # Buy target with intermediate
                        side = 'buy'
                        quantity = current_amount / price
                        current_amount = quantity
                    else:
                        # Sell intermediate for target
                        side = 'sell'
                        quantity = current_amount
                        current_amount = quantity * price
                        
                else:  # Third trade: target -> base
                    if base_asset == path.target_currency:
                        # Buy base with target
                        side = 'buy'
                        quantity = current_amount / price
                        current_amount = quantity
                    else:
                        # Sell target for base
                        side = 'sell'
                        quantity = current_amount
                        current_amount = quantity * price
                
                # Calculate fees
                fee_rate = self.exchange_manager.get_trading_fee(symbol)['taker']
                fee = current_amount * fee_rate
                total_fees += fee
                current_amount -= fee
                
                execution_order.append((symbol, side, quantity))
            
            # Calculate profit
            profit_amount = current_amount - base_amount
            profit_percentage = (profit_amount / base_amount) * 100
            
            if profit_percentage > 0:
                return ArbitrageOpportunity(
                    path=[str(path)],
                    profit_percentage=profit_percentage,
                    profit_amount=profit_amount,
                    base_amount=base_amount,
                    execution_order=execution_order,
                    timestamp=time.time(),
                    fees_total=total_fees
                )
            
            return None
            
        except Exception as e:
            log_error(f"Error calculating arbitrage opportunity for {path}", e)
            return None
    
    async def _should_execute_opportunity(self, opportunity: ArbitrageOpportunity) -> bool:
        """Determine if an arbitrage opportunity should be executed."""
        # Check minimum profit threshold after fees
        net_profit = opportunity.profit_percentage - (opportunity.fees_total / opportunity.base_amount * 100)
        if net_profit < config.min_profit_threshold * 100:
            return False
        
        # Check slippage protection
        if net_profit < config.max_slippage * 100 * 2:  # 2x slippage buffer
            return False
        
        # Check if we have sufficient balance
        # This would require checking actual account balances
        
        return True
    
    async def _execute_arbitrage(self, opportunity: ArbitrageOpportunity):
        """Execute a triangular arbitrage opportunity."""
        try:
            logger.info(f"Executing arbitrage opportunity: {opportunity.profit_percentage:.4f}% profit")
            
            trades = []
            for symbol, side, quantity in opportunity.execution_order:
                trade = await self.exchange_manager.place_market_order(symbol, side, quantity)
                if not trade:
                    logger.error(f"Failed to execute trade: {side} {quantity} {symbol}")
                    # TODO: Implement rollback mechanism
                    return False
                
                trades.append(trade)
                log_trade(f"Arbitrage trade executed: {side} {quantity} {symbol} @ {trade.price}")
            
            self.opportunities_executed += 1
            self.total_profit += opportunity.profit_amount
            
            logger.info(f"Arbitrage execution completed. Total profit: {self.total_profit:.4f}")
            return True
            
        except Exception as e:
            log_error("Error executing arbitrage opportunity", e)
            return False
    
    def stop_monitoring(self):
        """Stop monitoring for arbitrage opportunities."""
        self.is_running = False
        logger.info("Triangular arbitrage monitoring stopped")
    
    def get_statistics(self) -> Dict:
        """Get arbitrage engine statistics."""
        return {
            'opportunities_found': self.opportunities_found,
            'opportunities_executed': self.opportunities_executed,
            'total_profit': self.total_profit,
            'execution_rate': (self.opportunities_executed / max(self.opportunities_found, 1)) * 100,
            'triangular_paths': len(self.triangular_paths),
            'last_scan_time': self.last_scan_time,
            'scan_count': self.scan_count
        }
