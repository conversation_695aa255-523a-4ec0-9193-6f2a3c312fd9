"""Logging configuration for ArbTrader system."""

import sys
import os
from pathlib import Path
from loguru import logger
from .config import get_config

config = get_config()


def setup_logging() -> None:
    """Setup logging configuration for the application."""
    
    # Remove default handler
    logger.remove()
    
    # Create logs directory if it doesn't exist
    log_dir = Path(config.log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Console handler with colored output
    logger.add(
        sys.stdout,
        level=config.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # File handler for all logs
    logger.add(
        config.log_file,
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="100 MB",
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # Separate file for trading activities
    trading_log_file = str(Path(config.log_file).parent / "trading.log")
    logger.add(
        trading_log_file,
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
        filter=lambda record: "TRADE" in record["extra"],
        rotation="50 MB",
        retention="90 days",
        compression="zip"
    )
    
    # Error log file
    error_log_file = str(Path(config.log_file).parent / "errors.log")
    logger.add(
        error_log_file,
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="10 MB",
        retention="90 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )


def get_logger(name: str):
    """Get a logger instance with the specified name."""
    return logger.bind(name=name)


def log_trade(message: str, **kwargs):
    """Log trading activities."""
    logger.bind(TRADE=True).info(message, **kwargs)


def log_error(message: str, exception: Exception = None, **kwargs):
    """Log errors with optional exception details."""
    if exception:
        logger.error(f"{message}: {str(exception)}", **kwargs)
    else:
        logger.error(message, **kwargs)


def log_performance(strategy: str, profit: float, trades: int, **kwargs):
    """Log performance metrics."""
    logger.bind(PERFORMANCE=True).info(
        f"Strategy: {strategy} | Profit: {profit:.4f} | Trades: {trades}",
        **kwargs
    )


# Initialize logging
setup_logging()
