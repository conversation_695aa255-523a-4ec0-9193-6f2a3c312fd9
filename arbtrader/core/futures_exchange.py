"""Binance Futures exchange management and API integration."""

import asyncio
import time
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException
from binance import AsyncClient, BinanceSocketManager
import ccxt
from .config import get_config
from .logger import get_logger, log_error, log_trade

config = get_config()
logger = get_logger(__name__)


@dataclass
class FuturesOrderBook:
    """Futures order book data structure."""
    symbol: str
    bids: List[Tuple[float, float]]  # [(price, quantity), ...]
    asks: List[Tuple[float, float]]  # [(price, quantity), ...]
    timestamp: float


@dataclass
class FuturesTrade:
    """Futures trade data structure."""
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: float
    price: float
    fee: float
    timestamp: float
    order_id: str


@dataclass
class FuturesBalance:
    """Futures account balance data structure."""
    asset: str
    balance: float
    available: float
    locked: float


class FuturesExchangeManager:
    """Manages Binance Futures exchange connections and operations."""
    
    def __init__(self):
        self.client: Optional[Client] = None
        self.async_client: Optional[AsyncClient] = None
        self.socket_manager: Optional[BinanceSocketManager] = None
        self.trading_fees = {}
        self.symbols_info = {}
        self.is_connected = False
    
    async def initialize(self) -> bool:
        """Initialize futures exchange connections."""
        try:
            # Initialize synchronous client for futures
            self.client = Client(
                api_key=config.binance_api_key,
                api_secret=config.binance_secret_key,
                testnet=config.binance_testnet
            )
            
            # Initialize asynchronous client for futures
            self.async_client = await AsyncClient.create(
                api_key=config.binance_api_key,
                api_secret=config.binance_secret_key,
                testnet=config.binance_testnet
            )
            
            # Test futures connection
            account_info = await self.get_futures_account()
            if not account_info:
                return False
            
            # Initialize socket manager
            self.socket_manager = BinanceSocketManager(self.async_client)
            
            # Load futures exchange info
            await self._load_futures_exchange_info()
            
            self.is_connected = True
            logger.info("Successfully connected to Binance Futures API")
            return True
            
        except Exception as e:
            log_error("Failed to initialize futures exchange connection", e)
            return False
    
    async def get_futures_account(self) -> Optional[Dict]:
        """Get futures account information."""
        try:
            account = self.client.futures_account()
            return account
        except Exception as e:
            log_error("Failed to get futures account info", e)
            return None
    
    async def _load_futures_exchange_info(self):
        """Load futures exchange information."""
        try:
            exchange_info = self.client.futures_exchange_info()
            
            # Store symbol information
            for symbol_info in exchange_info['symbols']:
                symbol = symbol_info['symbol']
                self.symbols_info[symbol] = {
                    'status': symbol_info['status'],
                    'baseAsset': symbol_info['baseAsset'],
                    'quoteAsset': symbol_info['quoteAsset'],
                    'filters': symbol_info['filters'],
                    'contractType': symbol_info.get('contractType', 'PERPETUAL')
                }
            
            logger.info(f"Loaded info for {len(self.symbols_info)} futures symbols")
            
        except Exception as e:
            log_error("Failed to load futures exchange information", e)
    
    async def get_futures_orderbook(self, symbol: str, limit: int = 100) -> Optional[FuturesOrderBook]:
        """Get futures order book for a symbol."""
        try:
            depth = self.client.futures_order_book(symbol=symbol, limit=limit)
            
            return FuturesOrderBook(
                symbol=symbol,
                bids=[(float(bid[0]), float(bid[1])) for bid in depth['bids']],
                asks=[(float(ask[0]), float(ask[1])) for ask in depth['asks']],
                timestamp=time.time()
            )
            
        except Exception as e:
            log_error(f"Failed to get futures order book for {symbol}", e)
            return None
    
    async def get_futures_ticker_price(self, symbol: str) -> Optional[float]:
        """Get current futures ticker price for a symbol."""
        try:
            ticker = self.client.futures_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
            
        except Exception as e:
            log_error(f"Failed to get futures ticker price for {symbol}", e)
            return None
    
    async def get_all_futures_tickers(self) -> Dict[str, float]:
        """Get all futures ticker prices."""
        try:
            tickers = self.client.futures_symbol_ticker()
            return {ticker['symbol']: float(ticker['price']) for ticker in tickers}
            
        except Exception as e:
            log_error("Failed to get all futures ticker prices", e)
            return {}
    
    async def get_futures_account_balance(self) -> List[FuturesBalance]:
        """Get futures account balances."""
        try:
            account = self.client.futures_account()
            
            balances = []
            for balance in account['assets']:
                wallet_balance = float(balance['walletBalance'])
                available_balance = float(balance['availableBalance'])
                if wallet_balance > 0:
                    balances.append(FuturesBalance(
                        asset=balance['asset'],
                        balance=wallet_balance,
                        available=available_balance,
                        locked=wallet_balance - available_balance
                    ))
            
            return balances
            
        except Exception as e:
            log_error("Failed to get futures account balance", e)
            return []
    
    async def place_futures_market_order(self, symbol: str, side: str, quantity: float) -> Optional[FuturesTrade]:
        """Place a futures market order."""
        if config.trading_mode == "paper":
            return await self._simulate_futures_market_order(symbol, side, quantity)
        
        try:
            # Round quantity to proper precision
            quantity = self._round_quantity(symbol, quantity)
            
            order = self.client.futures_create_order(
                symbol=symbol,
                side=side.upper(),
                type='MARKET',
                quantity=quantity
            )
            
            # Get fill information
            avg_price = float(order.get('avgPrice', 0))
            if avg_price == 0:
                # If avgPrice not available, use current market price
                avg_price = await self.get_futures_ticker_price(symbol)
            
            executed_qty = float(order.get('executedQty', quantity))
            
            # Calculate fee (futures typically 0.04% for taker)
            fee = executed_qty * avg_price * 0.0004
            
            trade = FuturesTrade(
                symbol=symbol,
                side=side,
                quantity=executed_qty,
                price=avg_price,
                fee=fee,
                timestamp=time.time(),
                order_id=str(order['orderId'])
            )
            
            log_trade(f"Futures market order executed: {side} {executed_qty} {symbol} @ {avg_price}")
            return trade
            
        except BinanceOrderException as e:
            log_error(f"Futures order failed for {symbol}", e)
            return None
        except Exception as e:
            log_error(f"Unexpected error placing futures order for {symbol}", e)
            return None
    
    async def _simulate_futures_market_order(self, symbol: str, side: str, quantity: float) -> Optional[FuturesTrade]:
        """Simulate a futures market order for paper trading."""
        try:
            price = await self.get_futures_ticker_price(symbol)
            if not price:
                return None
            
            # Simulate slippage (0.02% for futures market orders)
            slippage = 0.0002
            if side.lower() == 'buy':
                price *= (1 + slippage)
            else:
                price *= (1 - slippage)
            
            # Calculate fee (0.04% for futures taker)
            fee = quantity * price * 0.0004
            
            trade = FuturesTrade(
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=price,
                fee=fee,
                timestamp=time.time(),
                order_id=f"PAPER_FUTURES_{int(time.time() * 1000)}"
            )
            
            log_trade(f"PAPER: Futures market order simulated: {side} {quantity} {symbol} @ {price}")
            return trade
            
        except Exception as e:
            log_error(f"Failed to simulate futures order for {symbol}", e)
            return None
    
    def _round_quantity(self, symbol: str, quantity: float) -> float:
        """Round quantity to symbol's step size."""
        symbol_info = self.get_symbol_info(symbol)
        if not symbol_info:
            return quantity
        
        for filter_info in symbol_info['filters']:
            if filter_info['filterType'] == 'LOT_SIZE':
                step_size = float(filter_info['stepSize'])
                precision = len(str(step_size).split('.')[-1].rstrip('0'))
                return round(quantity, precision)
        
        return quantity
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """Get futures symbol information."""
        return self.symbols_info.get(symbol)
    
    async def get_funding_rate(self, symbol: str) -> Optional[float]:
        """Get current funding rate for a perpetual futures symbol."""
        try:
            funding_rate = self.client.futures_funding_rate(symbol=symbol, limit=1)
            if funding_rate:
                return float(funding_rate[0]['fundingRate'])
            return None
        except Exception as e:
            log_error(f"Failed to get funding rate for {symbol}", e)
            return None
    
    async def close(self):
        """Close all connections."""
        if self.async_client:
            await self.async_client.close_connection()
        # Note: BinanceSocketManager doesn't have a close method in this version
        self.is_connected = False
        logger.info("Futures exchange connections closed")
