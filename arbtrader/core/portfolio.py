"""Portfolio management and main trading engine."""

import asyncio
import time
from typing import Dict, List, Optional
from dataclasses import dataclass
import signal
import sys

from .exchange import ExchangeManager
from .config import get_config

config = get_config()
from .logger import get_logger, log_trade, log_error
from ..data.market_data import MarketDataManager
from ..strategies.triangular_arbitrage import TriangularArbitrageEngine
from ..strategies.pairs_trading import PairsTradingEngine
from ..strategies.basis_trading import BasisTradingEngine
from ..risk.risk_manager import RiskManager

logger = get_logger(__name__)


@dataclass
class PortfolioStatus:
    """Portfolio status information."""
    total_value: float
    daily_pnl: float
    total_trades: int
    active_strategies: int
    risk_level: str
    uptime: float
    last_update: float


class PortfolioManager:
    """Main portfolio manager and trading engine coordinator."""
    
    def __init__(self):
        # Core components
        self.exchange_manager = ExchangeManager()
        self.market_data_manager = MarketDataManager(self.exchange_manager)
        self.risk_manager = RiskManager(self.exchange_manager, self.market_data_manager)
        
        # Strategy engines
        self.triangular_arbitrage = TriangularArbitrageEngine(
            self.exchange_manager, self.market_data_manager
        )
        self.pairs_trading = PairsTradingEngine(
            self.exchange_manager, self.market_data_manager
        )
        self.basis_trading = BasisTradingEngine(
            self.exchange_manager, self.market_data_manager
        )
        
        # Status tracking
        self.is_running = False
        self.start_time = 0
        self.total_trades = 0
        self.strategies_active = 0
        
        # Shutdown handling
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    async def initialize(self) -> bool:
        """Initialize the portfolio manager and all components."""
        try:
            logger.info("Initializing ArbTrader Portfolio Manager...")
            
            # Initialize exchange connection
            if not await self.exchange_manager.initialize():
                logger.error("Failed to initialize exchange manager")
                return False
            
            # Get available symbols for strategies
            symbols_info = self.exchange_manager.symbols_info
            active_symbols = [
                symbol for symbol, info in symbols_info.items()
                if info['status'] == 'TRADING' and 'SPOT' in info['permissions']
            ]
            
            # Filter to major trading pairs for better liquidity
            major_symbols = []
            for symbol in active_symbols:
                for base_currency in config.base_currencies:
                    if symbol.endswith(base_currency):
                        major_symbols.append(symbol)
                        break
            
            major_symbols = major_symbols[:100]  # Limit to top 100 for performance
            logger.info(f"Selected {len(major_symbols)} major trading symbols")
            
            # Start market data collection
            if not await self.market_data_manager.start(major_symbols):
                logger.error("Failed to start market data manager")
                return False
            
            # Wait for initial market data
            logger.info("Waiting for initial market data...")
            await asyncio.sleep(5)
            
            # Initialize strategy engines
            if config.enable_triangular_arbitrage:
                if await self.triangular_arbitrage.initialize():
                    logger.info("Triangular arbitrage engine initialized")
                    self.strategies_active += 1
                else:
                    logger.warning("Failed to initialize triangular arbitrage engine")
            
            if config.enable_pairs_trading:
                if await self.pairs_trading.initialize(major_symbols):
                    logger.info("Pairs trading engine initialized")
                    self.strategies_active += 1
                else:
                    logger.warning("Failed to initialize pairs trading engine")
            
            if config.enable_basis_trading:
                if await self.basis_trading.initialize():
                    logger.info("Basis trading engine initialized")
                    self.strategies_active += 1
                else:
                    logger.warning("Failed to initialize basis trading engine")
            
            if self.strategies_active == 0:
                logger.error("No strategies were successfully initialized")
                return False
            
            logger.info(f"Portfolio manager initialized with {self.strategies_active} active strategies")
            return True
            
        except Exception as e:
            log_error("Failed to initialize portfolio manager", e)
            return False
    
    async def start_trading(self):
        """Start the main trading loop."""
        if not self.exchange_manager.is_connected:
            logger.error("Exchange not connected")
            return
        
        self.is_running = True
        self.start_time = time.time()
        
        logger.info("Starting ArbTrader trading engine...")
        logger.info(f"Trading mode: {config.trading_mode}")
        logger.info(f"Active strategies: {self.strategies_active}")
        
        # Start strategy engines
        strategy_tasks = []
        
        if config.enable_triangular_arbitrage:
            task = asyncio.create_task(self.triangular_arbitrage.start_monitoring())
            strategy_tasks.append(task)
            logger.info("Triangular arbitrage monitoring started")
        
        if config.enable_pairs_trading:
            task = asyncio.create_task(self.pairs_trading.start_monitoring())
            strategy_tasks.append(task)
            logger.info("Pairs trading monitoring started")
        
        if config.enable_basis_trading:
            task = asyncio.create_task(self.basis_trading.start_monitoring())
            strategy_tasks.append(task)
            logger.info("Basis trading monitoring started")
        
        # Start risk monitoring
        risk_task = asyncio.create_task(self._risk_monitoring_loop())
        
        # Start status reporting
        status_task = asyncio.create_task(self._status_reporting_loop())
        
        try:
            # Wait for all tasks
            await asyncio.gather(*strategy_tasks, risk_task, status_task)
            
        except asyncio.CancelledError:
            logger.info("Trading tasks cancelled")
        except Exception as e:
            log_error("Error in trading loop", e)
        finally:
            await self._shutdown()
    
    async def _risk_monitoring_loop(self):
        """Risk monitoring loop."""
        while self.is_running:
            try:
                # Update risk metrics
                risk_metrics = await self.risk_manager.update_risk_metrics()
                
                if risk_metrics:
                    # Check if trading should be halted
                    if self.risk_manager.should_halt_trading():
                        logger.critical("RISK ALERT: Trading halted due to risk conditions")
                        await self.stop_trading()
                        break
                    
                    # Log risk warnings
                    if risk_metrics.risk_level.value in ['high', 'critical']:
                        logger.warning(f"Risk level: {risk_metrics.risk_level.value.upper()}")
                        logger.warning(f"Daily P&L: {risk_metrics.daily_pnl_percentage:.2f}%")
                        logger.warning(f"Max drawdown: {risk_metrics.max_drawdown:.2f}%")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                log_error("Error in risk monitoring", e)
                await asyncio.sleep(60)
    
    async def _status_reporting_loop(self):
        """Status reporting loop."""
        while self.is_running:
            try:
                await asyncio.sleep(300)  # Report every 5 minutes
                
                status = await self.get_portfolio_status()
                
                logger.info("=== PORTFOLIO STATUS ===")
                logger.info(f"Portfolio Value: ${status.total_value:.2f}")
                logger.info(f"Daily P&L: ${status.daily_pnl:.2f}")
                logger.info(f"Total Trades: {status.total_trades}")
                logger.info(f"Active Strategies: {status.active_strategies}")
                logger.info(f"Risk Level: {status.risk_level}")
                logger.info(f"Uptime: {status.uptime/3600:.1f} hours")
                
                # Strategy-specific statistics
                if config.enable_triangular_arbitrage:
                    tri_stats = self.triangular_arbitrage.get_statistics()
                    logger.info(f"Triangular Arbitrage - Opportunities: {tri_stats['opportunities_found']}, "
                              f"Executed: {tri_stats['opportunities_executed']}, "
                              f"Profit: ${tri_stats['total_profit']:.4f}")
                
                if config.enable_pairs_trading:
                    pairs_stats = self.pairs_trading.get_statistics()
                    logger.info(f"Pairs Trading - Signals: {pairs_stats['signals_generated']}, "
                              f"Trades: {pairs_stats['trades_executed']}, "
                              f"Profit: ${pairs_stats['total_profit']:.4f}")
                
                if config.enable_basis_trading:
                    basis_stats = self.basis_trading.get_statistics()
                    logger.info(f"Basis Trading - Opportunities: {basis_stats['opportunities_found']}, "
                              f"Trades: {basis_stats['trades_executed']}, "
                              f"Profit: ${basis_stats['total_profit']:.4f}")
                
                logger.info("========================")
                
            except Exception as e:
                log_error("Error in status reporting", e)
    
    async def get_portfolio_status(self) -> PortfolioStatus:
        """Get current portfolio status."""
        try:
            # Get risk summary
            risk_summary = self.risk_manager.get_risk_summary()
            
            # Calculate total trades
            total_trades = 0
            if config.enable_triangular_arbitrage:
                total_trades += self.triangular_arbitrage.get_statistics()['opportunities_executed']
            if config.enable_pairs_trading:
                total_trades += self.pairs_trading.get_statistics()['trades_executed']
            if config.enable_basis_trading:
                total_trades += self.basis_trading.get_statistics()['trades_executed']
            
            return PortfolioStatus(
                total_value=risk_summary['portfolio_value'],
                daily_pnl=risk_summary['daily_pnl'],
                total_trades=total_trades,
                active_strategies=self.strategies_active,
                risk_level=risk_summary['risk_level'],
                uptime=time.time() - self.start_time if self.start_time > 0 else 0,
                last_update=time.time()
            )
            
        except Exception as e:
            log_error("Error getting portfolio status", e)
            return PortfolioStatus(0, 0, 0, 0, "unknown", 0, time.time())
    
    async def stop_trading(self):
        """Stop all trading activities."""
        logger.info("Stopping trading activities...")
        
        self.is_running = False
        
        # Stop strategy engines
        if config.enable_triangular_arbitrage:
            self.triangular_arbitrage.stop_monitoring()
        
        if config.enable_pairs_trading:
            self.pairs_trading.stop_monitoring()
        
        if config.enable_basis_trading:
            self.basis_trading.stop_monitoring()
        
        logger.info("All trading activities stopped")
    
    async def _shutdown(self):
        """Graceful shutdown of all components."""
        logger.info("Shutting down ArbTrader...")
        
        try:
            # Stop market data
            await self.market_data_manager.stop()
            
            # Close exchange connections
            await self.exchange_manager.close()
            
            logger.info("ArbTrader shutdown complete")
            
        except Exception as e:
            log_error("Error during shutdown", e)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.is_running = False
    
    async def emergency_stop(self):
        """Emergency stop - close all positions and halt trading."""
        logger.critical("EMERGENCY STOP INITIATED")
        
        try:
            # Stop all trading
            await self.stop_trading()
            
            # TODO: Close all open positions
            # This would require implementing position tracking and closing logic
            
            logger.critical("Emergency stop completed")
            
        except Exception as e:
            log_error("Error during emergency stop", e)
