"""Exchange management and API integration."""

import asyncio
import time
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException
from binance import AsyncClient, BinanceSocketManager
import ccxt
from .config import get_config

config = get_config()
from .logger import get_logger, log_error, log_trade

logger = get_logger(__name__)


@dataclass
class OrderBook:
    """Order book data structure."""
    symbol: str
    bids: List[Tuple[float, float]]  # [(price, quantity), ...]
    asks: List[Tuple[float, float]]  # [(price, quantity), ...]
    timestamp: float


@dataclass
class Trade:
    """Trade data structure."""
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: float
    price: float
    fee: float
    timestamp: float
    order_id: str


@dataclass
class Balance:
    """Account balance data structure."""
    asset: str
    free: float
    locked: float
    total: float


class RateLimiter:
    """Rate limiter for API calls."""
    
    def __init__(self, max_calls: int = 1200, time_window: int = 60):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    async def acquire(self):
        """Acquire permission to make an API call."""
        now = time.time()
        
        # Remove old calls outside the time window
        self.calls = [call_time for call_time in self.calls 
                     if now - call_time < self.time_window]
        
        # Check if we can make a call
        if len(self.calls) >= self.max_calls:
            sleep_time = self.time_window - (now - self.calls[0])
            if sleep_time > 0:
                logger.warning(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)
        
        self.calls.append(now)


class ExchangeManager:
    """Manages exchange connections and operations."""
    
    def __init__(self):
        self.client: Optional[Client] = None
        self.async_client: Optional[AsyncClient] = None
        self.socket_manager: Optional[BinanceSocketManager] = None
        self.rate_limiter = RateLimiter()
        self.trading_fees = {}
        self.symbols_info = {}
        self.is_connected = False
    
    async def initialize(self) -> bool:
        """Initialize exchange connections."""
        try:
            # Initialize synchronous client
            self.client = Client(
                api_key=config.binance_api_key,
                api_secret=config.binance_secret_key,
                testnet=config.binance_testnet
            )
            
            # Initialize asynchronous client
            self.async_client = await AsyncClient.create(
                api_key=config.binance_api_key,
                api_secret=config.binance_secret_key,
                testnet=config.binance_testnet
            )
            
            # Test connection
            await self.rate_limiter.acquire()
            account_info = await self.async_client.get_account()
            
            # Initialize socket manager
            self.socket_manager = BinanceSocketManager(self.async_client)
            
            # Load exchange info
            await self._load_exchange_info()
            
            self.is_connected = True
            logger.info("Successfully connected to Binance API")
            return True
            
        except Exception as e:
            log_error("Failed to initialize exchange connection", e)
            return False
    
    async def _load_exchange_info(self):
        """Load exchange information including trading fees and symbol info."""
        try:
            await self.rate_limiter.acquire()
            exchange_info = await self.async_client.get_exchange_info()
            
            # Store symbol information
            for symbol_info in exchange_info['symbols']:
                symbol = symbol_info['symbol']
                self.symbols_info[symbol] = {
                    'status': symbol_info['status'],
                    'baseAsset': symbol_info['baseAsset'],
                    'quoteAsset': symbol_info['quoteAsset'],
                    'filters': symbol_info['filters'],
                    'permissions': symbol_info['permissions']
                }
            
            # Load trading fees
            await self.rate_limiter.acquire()
            trade_fee = await self.async_client.get_trade_fee()
            
            for fee_info in trade_fee['tradeFee']:
                symbol = fee_info['symbol']
                self.trading_fees[symbol] = {
                    'maker': float(fee_info['maker']),
                    'taker': float(fee_info['taker'])
                }
            
            logger.info(f"Loaded info for {len(self.symbols_info)} symbols")
            
        except Exception as e:
            log_error("Failed to load exchange information", e)
    
    async def get_orderbook(self, symbol: str, limit: int = 100) -> Optional[OrderBook]:
        """Get order book for a symbol."""
        try:
            await self.rate_limiter.acquire()
            depth = await self.async_client.get_order_book(symbol=symbol, limit=limit)
            
            return OrderBook(
                symbol=symbol,
                bids=[(float(bid[0]), float(bid[1])) for bid in depth['bids']],
                asks=[(float(ask[0]), float(ask[1])) for ask in depth['asks']],
                timestamp=time.time()
            )
            
        except Exception as e:
            log_error(f"Failed to get order book for {symbol}", e)
            return None
    
    async def get_ticker_price(self, symbol: str) -> Optional[float]:
        """Get current ticker price for a symbol."""
        try:
            await self.rate_limiter.acquire()
            ticker = await self.async_client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
            
        except Exception as e:
            log_error(f"Failed to get ticker price for {symbol}", e)
            return None
    
    async def get_all_tickers(self) -> Dict[str, float]:
        """Get all ticker prices."""
        try:
            await self.rate_limiter.acquire()
            tickers = await self.async_client.get_all_tickers()
            return {ticker['symbol']: float(ticker['price']) for ticker in tickers}
            
        except Exception as e:
            log_error("Failed to get all ticker prices", e)
            return {}
    
    async def get_account_balance(self) -> List[Balance]:
        """Get account balances."""
        try:
            await self.rate_limiter.acquire()
            account = await self.async_client.get_account()
            
            balances = []
            for balance in account['balances']:
                free = float(balance['free'])
                locked = float(balance['locked'])
                if free > 0 or locked > 0:
                    balances.append(Balance(
                        asset=balance['asset'],
                        free=free,
                        locked=locked,
                        total=free + locked
                    ))
            
            return balances
            
        except Exception as e:
            log_error("Failed to get account balance", e)
            return []
    
    def get_trading_fee(self, symbol: str) -> Dict[str, float]:
        """Get trading fees for a symbol."""
        return self.trading_fees.get(symbol, {'maker': 0.001, 'taker': 0.001})
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """Get symbol information."""
        return self.symbols_info.get(symbol)
    
    async def place_market_order(self, symbol: str, side: str, quantity: float) -> Optional[Trade]:
        """Place a market order."""
        if config.trading_mode == "paper":
            return await self._simulate_market_order(symbol, side, quantity)

        try:
            await self.rate_limiter.acquire()

            # Get symbol info for precision
            symbol_info = self.get_symbol_info(symbol)
            if not symbol_info:
                logger.error(f"Symbol info not found for {symbol}")
                return None

            # Round quantity to proper precision
            quantity = self._round_quantity(symbol, quantity)

            order = await self.async_client.order_market(
                symbol=symbol,
                side=side.upper(),
                quantity=quantity
            )

            # Calculate average price and fees
            fills = order.get('fills', [])
            if not fills:
                logger.error(f"No fills in order response for {symbol}")
                return None

            total_qty = sum(float(fill['qty']) for fill in fills)
            total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in fills)
            avg_price = total_cost / total_qty if total_qty > 0 else 0

            total_fee = sum(float(fill['commission']) for fill in fills)

            trade = Trade(
                symbol=symbol,
                side=side,
                quantity=total_qty,
                price=avg_price,
                fee=total_fee,
                timestamp=time.time(),
                order_id=order['orderId']
            )

            log_trade(f"Market order executed: {side} {total_qty} {symbol} @ {avg_price}")
            return trade

        except BinanceOrderException as e:
            log_error(f"Order failed for {symbol}", e)
            return None
        except Exception as e:
            log_error(f"Unexpected error placing order for {symbol}", e)
            return None

    async def _simulate_market_order(self, symbol: str, side: str, quantity: float) -> Optional[Trade]:
        """Simulate a market order for paper trading."""
        try:
            price = await self.get_ticker_price(symbol)
            if not price:
                return None

            # Simulate slippage (0.01% for market orders)
            slippage = 0.0001
            if side.lower() == 'buy':
                price *= (1 + slippage)
            else:
                price *= (1 - slippage)

            # Calculate fee
            fee_rate = self.get_trading_fee(symbol)['taker']
            fee = quantity * price * fee_rate

            trade = Trade(
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=price,
                fee=fee,
                timestamp=time.time(),
                order_id=f"PAPER_{int(time.time() * 1000)}"
            )

            log_trade(f"PAPER: Market order simulated: {side} {quantity} {symbol} @ {price}")
            return trade

        except Exception as e:
            log_error(f"Failed to simulate order for {symbol}", e)
            return None

    def _round_quantity(self, symbol: str, quantity: float) -> float:
        """Round quantity to symbol's step size."""
        symbol_info = self.get_symbol_info(symbol)
        if not symbol_info:
            return quantity

        for filter_info in symbol_info['filters']:
            if filter_info['filterType'] == 'LOT_SIZE':
                step_size = float(filter_info['stepSize'])
                precision = len(str(step_size).split('.')[-1].rstrip('0'))
                return round(quantity, precision)

        return quantity

    async def close(self):
        """Close all connections."""
        if self.async_client:
            await self.async_client.close_connection()
        if self.socket_manager:
            await self.socket_manager.close()
        self.is_connected = False
        logger.info("Exchange connections closed")
