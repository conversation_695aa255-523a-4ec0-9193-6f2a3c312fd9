"""Configuration management for ArbTrader system."""

import os
from typing import Optional, List
from pydantic import Field, validator
from pydantic_settings import BaseSettings
from enum import Enum


class TradingMode(str, Enum):
    """Trading mode enumeration."""
    PAPER = "paper"
    LIVE = "live"


class PositionSizeMethod(str, Enum):
    """Position sizing method enumeration."""
    FIXED = "fixed"
    KELLY = "kelly"
    VOLATILITY = "volatility"


class Config(BaseSettings):
    """Main configuration class for ArbTrader."""
    
    # API Configuration
    binance_api_key: str = Field(..., env="BINANCE_API_KEY")
    binance_secret_key: str = Field(..., env="BINANCE_SECRET_KEY")
    binance_testnet: bool = Field(True, env="BINANCE_TESTNET")
    
    # Trading Configuration
    trading_mode: TradingMode = Field(TradingMode.PAPER, env="TRADING_MODE")
    max_position_size: float = Field(1000.0, env="MAX_POSITION_SIZE")
    risk_per_trade: float = Field(0.02, env="RISK_PER_TRADE")
    max_daily_loss: float = Field(0.05, env="MAX_DAILY_LOSS")
    
    # Strategy Enablement
    enable_triangular_arbitrage: bool = Field(True, env="ENABLE_TRIANGULAR_ARBITRAGE")
    enable_pairs_trading: bool = Field(True, env="ENABLE_PAIRS_TRADING")
    enable_basis_trading: bool = Field(True, env="ENABLE_BASIS_TRADING")
    
    # Triangular Arbitrage Settings
    min_profit_threshold: float = Field(0.001, env="MIN_PROFIT_THRESHOLD")
    max_slippage: float = Field(0.0005, env="MAX_SLIPPAGE")
    
    # Pairs Trading Settings
    correlation_threshold: float = Field(0.8, env="CORRELATION_THRESHOLD")
    zscore_entry: float = Field(2.0, env="ZSCORE_ENTRY")
    zscore_exit: float = Field(0.5, env="ZSCORE_EXIT")
    lookback_period: int = Field(30, env="LOOKBACK_PERIOD")
    
    # Basis Trading Settings
    min_basis_spread: float = Field(0.002, env="MIN_BASIS_SPREAD")
    funding_rate_threshold: float = Field(0.0001, env="FUNDING_RATE_THRESHOLD")
    
    # Database Configuration
    database_url: str = Field("sqlite:///arbtrader.db", env="DATABASE_URL")
    redis_url: str = Field("redis://localhost:6379/0", env="REDIS_URL")
    
    # Logging Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("logs/arbtrader.log", env="LOG_FILE")
    
    # Monitoring Configuration
    prometheus_port: int = Field(8000, env="PROMETHEUS_PORT")
    enable_telegram_alerts: bool = Field(False, env="ENABLE_TELEGRAM_ALERTS")
    telegram_bot_token: Optional[str] = Field(None, env="TELEGRAM_BOT_TOKEN")
    telegram_chat_id: Optional[str] = Field(None, env="TELEGRAM_CHAT_ID")
    
    # Risk Management
    position_size_method: PositionSizeMethod = Field(
        PositionSizeMethod.FIXED, env="POSITION_SIZE_METHOD"
    )
    stop_loss_percentage: float = Field(0.02, env="STOP_LOSS_PERCENTAGE")
    take_profit_percentage: float = Field(0.01, env="TAKE_PROFIT_PERCENTAGE")
    
    # Trading Pairs Configuration
    base_currencies: List[str] = Field(
        default=["BTC", "ETH", "BNB", "USDT", "BUSD"],
        env="BASE_CURRENCIES"
    )
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator("risk_per_trade", "max_daily_loss")
    def validate_risk_percentages(cls, v):
        """Validate risk percentages are between 0 and 1."""
        if not 0 < v <= 1:
            raise ValueError("Risk percentages must be between 0 and 1")
        return v
    
    @validator("correlation_threshold")
    def validate_correlation(cls, v):
        """Validate correlation threshold is between 0 and 1."""
        if not 0 <= v <= 1:
            raise ValueError("Correlation threshold must be between 0 and 1")
        return v
    
    @validator("zscore_entry", "zscore_exit")
    def validate_zscore(cls, v):
        """Validate z-score values are positive."""
        if v <= 0:
            raise ValueError("Z-score values must be positive")
        return v


# Global configuration instance - will be initialized when needed
config = None

def get_config() -> Config:
    """Get or create global configuration instance."""
    global config
    if config is None:
        try:
            config = Config()
        except Exception:
            # If config fails to load (e.g., missing API keys), create with defaults
            config = Config(
                binance_api_key="",
                binance_secret_key=""
            )
    return config
