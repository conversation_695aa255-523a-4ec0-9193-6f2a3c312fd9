"""Real-time market data management and WebSocket connections."""

import asyncio
import json
import time
from typing import Dict, List, Optional, Callable, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import pandas as pd
from binance import BinanceSocketManager
from ..core.futures_exchange import FuturesExchangeManager, FuturesOrderBook
from ..core.logger import get_logger, log_error

logger = get_logger(__name__)


@dataclass
class TickerData:
    """Ticker data structure."""
    symbol: str
    price: float
    price_change: float
    price_change_percent: float
    volume: float
    timestamp: float


@dataclass
class KlineData:
    """Kline/candlestick data structure."""
    symbol: str
    open_time: int
    close_time: int
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    timestamp: float


class MarketDataManager:
    """Manages real-time market data collection and distribution."""
    
    def __init__(self, exchange_manager: FuturesExchangeManager):
        self.exchange_manager = exchange_manager
        self.socket_manager: Optional[BinanceSocketManager] = None
        self.active_streams = {}
        self.subscribers = defaultdict(list)
        
        # Data storage
        self.ticker_data: Dict[str, TickerData] = {}
        self.orderbook_data: Dict[str, FuturesOrderBook] = {}
        self.kline_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Price history for analysis
        self.price_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        
        self.is_running = False
    
    async def start(self, symbols: List[str]):
        """Start market data collection for specified symbols."""
        if not self.exchange_manager.is_connected:
            logger.error("Exchange manager not connected")
            return False

        try:
            self.is_running = True

            # For now, we'll use REST API polling instead of WebSocket
            # This avoids the WebSocket compatibility issues
            logger.info(f"Market data collection started for {len(symbols)} symbols (REST mode)")

            # Start background task to poll market data
            asyncio.create_task(self._poll_market_data(symbols))

            return True

        except Exception as e:
            log_error("Failed to start market data collection", e)
            return False

    async def _poll_market_data(self, symbols: List[str]):
        """Poll market data using REST API instead of WebSocket."""
        while self.is_running:
            try:
                # Get all ticker prices
                all_tickers = await self.exchange_manager.get_all_futures_tickers()

                for symbol in symbols:
                    if symbol in all_tickers:
                        price = all_tickers[symbol]

                        # Create ticker data
                        ticker = TickerData(
                            symbol=symbol,
                            price=price,
                            price_change=0.0,  # We'll calculate this later
                            price_change_percent=0.0,
                            volume=0.0,
                            timestamp=time.time()
                        )

                        self.ticker_data[symbol] = ticker

                        # Update price history
                        self.price_history[symbol].append(price)

                        # Notify subscribers
                        await self._notify_subscribers('ticker', symbol, ticker)

                # Sleep for 1 second before next poll
                await asyncio.sleep(1)

            except Exception as e:
                log_error("Error polling market data", e)
                await asyncio.sleep(5)  # Wait longer on error
    
    # WebSocket methods removed - using REST API polling instead
    
    async def _start_orderbook_streams(self, symbols: List[str]):
        """Start orderbook streams for symbols."""
        try:
            # Limit to top symbols to avoid too many connections
            priority_symbols = symbols[:20]  # Top 20 symbols
            
            for symbol in priority_symbols:
                symbol_lower = symbol.lower()
                stream = self.socket_manager.depth_socket(symbol_lower, depth=20)
                
                self.active_streams[f'depth_{symbol}'] = stream
                
                # Start stream in background
                asyncio.create_task(self._handle_orderbook_stream(stream, symbol))
                
        except Exception as e:
            log_error("Error starting orderbook streams", e)
    
    async def _handle_orderbook_stream(self, stream, symbol: str):
        """Handle orderbook stream for a symbol."""
        try:
            async with stream as ds:
                async for msg in ds:
                    await self._handle_orderbook_message(msg, symbol)
        except Exception as e:
            log_error(f"Error in orderbook stream for {symbol}", e)
    
    async def _start_kline_streams(self, symbols: List[str]):
        """Start kline streams for symbols."""
        try:
            # Start 1-minute kline streams for analysis
            priority_symbols = symbols[:50]  # Top 50 symbols
            
            for symbol in priority_symbols:
                symbol_lower = symbol.lower()
                stream = self.socket_manager.kline_socket(symbol_lower, interval='1m')
                
                self.active_streams[f'kline_{symbol}'] = stream
                
                # Start stream in background
                asyncio.create_task(self._handle_kline_stream(stream, symbol))
                
        except Exception as e:
            log_error("Error starting kline streams", e)
    
    async def _handle_kline_stream(self, stream, symbol: str):
        """Handle kline stream for a symbol."""
        try:
            async with stream as ks:
                async for msg in ks:
                    await self._handle_kline_message(msg, symbol)
        except Exception as e:
            log_error(f"Error in kline stream for {symbol}", e)
    
    async def _handle_ticker_message(self, msg):
        """Handle ticker WebSocket message."""
        try:
            if msg['stream'].endswith('@ticker'):
                data = msg['data']
                symbol = data['s']
                
                ticker = TickerData(
                    symbol=symbol,
                    price=float(data['c']),
                    price_change=float(data['p']),
                    price_change_percent=float(data['P']),
                    volume=float(data['v']),
                    timestamp=time.time()
                )
                
                self.ticker_data[symbol] = ticker
                
                # Update price history
                self.price_history[symbol].append(ticker.price)
                
                # Notify subscribers
                await self._notify_subscribers('ticker', symbol, ticker)
                
        except Exception as e:
            log_error("Error handling ticker message", e)
    
    async def _handle_orderbook_message(self, msg, symbol: str):
        """Handle orderbook WebSocket message."""
        try:
            data = msg
            
            orderbook = OrderBook(
                symbol=symbol,
                bids=[(float(bid[0]), float(bid[1])) for bid in data['bids']],
                asks=[(float(ask[0]), float(ask[1])) for ask in data['asks']],
                timestamp=time.time()
            )
            
            self.orderbook_data[symbol] = orderbook
            
            # Notify subscribers
            await self._notify_subscribers('orderbook', symbol, orderbook)
            
        except Exception as e:
            log_error(f"Error handling orderbook message for {symbol}", e)
    
    async def _handle_kline_message(self, msg, symbol: str):
        """Handle kline WebSocket message."""
        try:
            data = msg['k']
            
            # Only process closed klines
            if data['x']:  # kline is closed
                kline = KlineData(
                    symbol=symbol,
                    open_time=data['t'],
                    close_time=data['T'],
                    open_price=float(data['o']),
                    high_price=float(data['h']),
                    low_price=float(data['l']),
                    close_price=float(data['c']),
                    volume=float(data['v']),
                    timestamp=time.time()
                )
                
                self.kline_data[symbol].append(kline)
                
                # Notify subscribers
                await self._notify_subscribers('kline', symbol, kline)
                
        except Exception as e:
            log_error(f"Error handling kline message for {symbol}", e)
    
    async def _notify_subscribers(self, data_type: str, symbol: str, data):
        """Notify subscribers of new data."""
        key = f"{data_type}_{symbol}"
        for callback in self.subscribers[key]:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                log_error(f"Error in subscriber callback for {key}", e)
    
    def subscribe(self, data_type: str, symbol: str, callback: Callable):
        """Subscribe to market data updates."""
        key = f"{data_type}_{symbol}"
        self.subscribers[key].append(callback)
        logger.info(f"Subscribed to {key}")
    
    def unsubscribe(self, data_type: str, symbol: str, callback: Callable):
        """Unsubscribe from market data updates."""
        key = f"{data_type}_{symbol}"
        if callback in self.subscribers[key]:
            self.subscribers[key].remove(callback)
            logger.info(f"Unsubscribed from {key}")
    
    def get_ticker(self, symbol: str) -> Optional[TickerData]:
        """Get latest ticker data for a symbol."""
        return self.ticker_data.get(symbol)
    
    def get_orderbook(self, symbol: str) -> Optional[FuturesOrderBook]:
        """Get latest orderbook for a symbol."""
        return self.orderbook_data.get(symbol)
    
    def get_price_history(self, symbol: str, periods: int = 100) -> List[float]:
        """Get price history for a symbol."""
        history = list(self.price_history[symbol])
        return history[-periods:] if len(history) > periods else history
    
    def get_kline_dataframe(self, symbol: str, periods: int = 100) -> pd.DataFrame:
        """Get kline data as pandas DataFrame."""
        klines = list(self.kline_data[symbol])
        if not klines:
            return pd.DataFrame()
        
        # Take last N periods
        recent_klines = klines[-periods:] if len(klines) > periods else klines
        
        # Convert to DataFrame
        data = [asdict(kline) for kline in recent_klines]
        df = pd.DataFrame(data)
        
        if not df.empty:
            df['datetime'] = pd.to_datetime(df['open_time'], unit='ms')
            df.set_index('datetime', inplace=True)
        
        return df
    
    async def stop(self):
        """Stop market data collection."""
        self.is_running = False
        
        # Close all active streams
        for stream_name, stream in self.active_streams.items():
            try:
                await stream.__aexit__(None, None, None)
            except Exception as e:
                log_error(f"Error closing stream {stream_name}", e)
        
        self.active_streams.clear()
        logger.info("Market data collection stopped")
