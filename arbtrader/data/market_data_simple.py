"""Simple market data management using REST API only - NO WebSocket errors!"""

import asyncio
import time
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from collections import defaultdict, deque
import pandas as pd
from ..core.futures_exchange import FuturesExchangeManager, FuturesOrderBook
from ..core.logger import get_logger, log_error

logger = get_logger(__name__)


@dataclass
class TickerData:
    """Ticker data structure."""
    symbol: str
    price: float
    price_change: float
    price_change_percent: float
    volume: float
    timestamp: float


@dataclass
class KlineData:
    """Kline/candlestick data structure."""
    symbol: str
    open_time: int
    close_time: int
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    timestamp: float


class SimpleMarketDataManager:
    """Simple market data manager using REST API only - NO WebSocket errors!"""
    
    def __init__(self, exchange_manager: FuturesExchangeManager):
        self.exchange_manager = exchange_manager
        
        # Data storage
        self.ticker_data: Dict[str, TickerData] = {}
        self.orderbook_data: Dict[str, FuturesOrderBook] = {}
        self.kline_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Price history for analysis
        self.price_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        
        # Subscribers
        self.subscribers = defaultdict(list)
        
        self.is_running = False
        self.symbols = []
    
    async def start(self, symbols: List[str]):
        """Start market data collection for specified symbols."""
        if not self.exchange_manager.is_connected:
            logger.error("Exchange manager not connected")
            return False
        
        try:
            self.is_running = True
            self.symbols = symbols
            
            logger.info(f"Market data collection started for {len(symbols)} symbols (REST mode)")
            
            # Start background task to poll market data
            asyncio.create_task(self._poll_market_data())
            
            return True
            
        except Exception as e:
            log_error("Failed to start market data collection", e)
            return False
    
    async def _poll_market_data(self):
        """Poll market data using REST API - NO WebSocket errors!"""
        while self.is_running:
            try:
                # Get all ticker prices
                all_tickers = await self.exchange_manager.get_all_futures_tickers()
                
                for symbol in self.symbols:
                    if symbol in all_tickers:
                        price = all_tickers[symbol]
                        
                        # Calculate price change if we have previous data
                        price_change = 0.0
                        price_change_percent = 0.0
                        if symbol in self.ticker_data:
                            old_price = self.ticker_data[symbol].price
                            price_change = price - old_price
                            if old_price > 0:
                                price_change_percent = (price_change / old_price) * 100
                        
                        # Create ticker data
                        ticker = TickerData(
                            symbol=symbol,
                            price=price,
                            price_change=price_change,
                            price_change_percent=price_change_percent,
                            volume=0.0,  # Volume not available in simple ticker
                            timestamp=time.time()
                        )
                        
                        self.ticker_data[symbol] = ticker
                        
                        # Update price history
                        self.price_history[symbol].append(price)
                        
                        # Notify subscribers
                        await self._notify_subscribers('ticker', symbol, ticker)
                
                # Sleep for 2 seconds before next poll (reasonable rate)
                await asyncio.sleep(2)
                
            except Exception as e:
                log_error("Error polling market data", e)
                await asyncio.sleep(10)  # Wait longer on error
    
    async def _notify_subscribers(self, data_type: str, symbol: str, data):
        """Notify subscribers of new data."""
        key = f"{data_type}_{symbol}"
        for callback in self.subscribers[key]:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                log_error(f"Error in subscriber callback for {key}", e)
    
    def subscribe(self, data_type: str, symbol: str, callback: Callable):
        """Subscribe to market data updates."""
        key = f"{data_type}_{symbol}"
        self.subscribers[key].append(callback)
        logger.info(f"Subscribed to {key}")
    
    def unsubscribe(self, data_type: str, symbol: str, callback: Callable):
        """Unsubscribe from market data updates."""
        key = f"{data_type}_{symbol}"
        if callback in self.subscribers[key]:
            self.subscribers[key].remove(callback)
            logger.info(f"Unsubscribed from {key}")
    
    def get_ticker(self, symbol: str) -> Optional[TickerData]:
        """Get latest ticker data for a symbol."""
        return self.ticker_data.get(symbol)
    
    def get_orderbook(self, symbol: str) -> Optional[FuturesOrderBook]:
        """Get latest orderbook for a symbol."""
        # For now, we'll get orderbook on demand via REST API
        try:
            return asyncio.create_task(self.exchange_manager.get_futures_orderbook(symbol))
        except:
            return None
    
    def get_price_history(self, symbol: str, periods: int = 100) -> List[float]:
        """Get price history for a symbol."""
        history = list(self.price_history[symbol])
        return history[-periods:] if len(history) > periods else history
    
    def get_kline_dataframe(self, symbol: str, periods: int = 100) -> pd.DataFrame:
        """Get kline data as pandas DataFrame."""
        # For now, return empty DataFrame - can be enhanced later
        return pd.DataFrame()
    
    async def stop(self):
        """Stop market data collection."""
        self.is_running = False
        logger.info("Market data collection stopped")


# Alias for compatibility
MarketDataManager = SimpleMarketDataManager
