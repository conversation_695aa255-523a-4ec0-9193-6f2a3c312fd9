# Binance API Configuration
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=true

# Trading Configuration
TRADING_MODE=paper  # paper, live
MAX_POSITION_SIZE=1000  # USD
RISK_PER_TRADE=0.02  # 2% risk per trade
MAX_DAILY_LOSS=0.05  # 5% max daily loss

# Strategy Configuration
ENABLE_TRIANGULAR_ARBITRAGE=true
ENABLE_PAIRS_TRADING=true
ENABLE_BASIS_TRADING=true

# Triangular Arbitrage Settings
MIN_PROFIT_THRESHOLD=0.001  # 0.1% minimum profit
MAX_SLIPPAGE=0.0005  # 0.05% max slippage

# Pairs Trading Settings
CORRELATION_THRESHOLD=0.8
ZSCORE_ENTRY=2.0
ZSCORE_EXIT=0.5
LOOKBACK_PERIOD=30  # days

# Basis Trading Settings
MIN_BASIS_SPREAD=0.002  # 0.2% minimum spread
FUNDING_RATE_THRESHOLD=0.0001

# Database Configuration
DATABASE_URL=sqlite:///arbtrader.db
REDIS_URL=redis://localhost:6379/0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/arbtrader.log

# Monitoring Configuration
PROMETHEUS_PORT=8000
ENABLE_TELEGRAM_ALERTS=false
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=

# Risk Management
POSITION_SIZE_METHOD=fixed  # fixed, kelly, volatility
STOP_LOSS_PERCENTAGE=0.02
TAKE_PROFIT_PERCENTAGE=0.01
