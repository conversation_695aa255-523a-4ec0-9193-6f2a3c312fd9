#!/usr/bin/env python3
"""
Test script for Binance Futures API integration.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.futures_exchange import FuturesExchangeManager
from arbtrader.core.config import get_config

async def test_futures_system():
    """Test the futures trading system."""
    print("🚀 Testing Binance Futures Trading System")
    print("=" * 50)
    
    config = get_config()
    print(f"Trading Mode: {config.trading_mode}")
    print(f"Testnet: {config.binance_testnet}")
    print()
    
    # Initialize futures exchange manager
    futures_manager = FuturesExchangeManager()
    
    print("🔌 Initializing Futures Exchange Manager...")
    if not await futures_manager.initialize():
        print("❌ Failed to initialize futures exchange manager")
        return False
    
    print("✅ Futures Exchange Manager initialized successfully!")
    print()
    
    # Test account info
    print("💰 Testing Account Information...")
    balances = await futures_manager.get_futures_account_balance()
    if balances:
        print("✅ Account balances retrieved:")
        for balance in balances:
            print(f"   {balance.asset}: {balance.balance} (available: {balance.available})")
    else:
        print("❌ Failed to get account balances")
    print()
    
    # Test market data
    print("📊 Testing Market Data...")
    
    # Test single ticker
    btc_price = await futures_manager.get_futures_ticker_price("BTCUSDT")
    if btc_price:
        print(f"✅ BTC/USDT Futures Price: ${btc_price:,.2f}")
    else:
        print("❌ Failed to get BTC price")
    
    # Test order book
    orderbook = await futures_manager.get_futures_orderbook("BTCUSDT", limit=5)
    if orderbook:
        print(f"✅ Order book retrieved: {len(orderbook.bids)} bids, {len(orderbook.asks)} asks")
        print(f"   Best bid: ${orderbook.bids[0][0]:,.2f}")
        print(f"   Best ask: ${orderbook.asks[0][0]:,.2f}")
    else:
        print("❌ Failed to get order book")
    
    # Test funding rate
    funding_rate = await futures_manager.get_funding_rate("BTCUSDT")
    if funding_rate is not None:
        print(f"✅ Funding rate: {funding_rate * 100:.4f}%")
    else:
        print("❌ Failed to get funding rate")
    print()
    
    # Test paper trading
    print("📝 Testing Paper Trading...")
    if config.trading_mode == "paper":
        # Simulate a small trade
        trade = await futures_manager.place_futures_market_order("BTCUSDT", "buy", 0.001)
        if trade:
            print(f"✅ Paper trade executed:")
            print(f"   Symbol: {trade.symbol}")
            print(f"   Side: {trade.side}")
            print(f"   Quantity: {trade.quantity}")
            print(f"   Price: ${trade.price:,.2f}")
            print(f"   Fee: ${trade.fee:.4f}")
        else:
            print("❌ Failed to execute paper trade")
    else:
        print("ℹ️  Skipping trade test (not in paper mode)")
    print()
    
    # Test all tickers (limited)
    print("📈 Testing All Tickers...")
    all_tickers = await futures_manager.get_all_futures_tickers()
    if all_tickers:
        print(f"✅ Retrieved {len(all_tickers)} ticker prices")
        # Show a few major ones
        major_symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT"]
        for symbol in major_symbols:
            if symbol in all_tickers:
                print(f"   {symbol}: ${all_tickers[symbol]:,.2f}")
    else:
        print("❌ Failed to get all tickers")
    print()
    
    # Clean up
    await futures_manager.close()
    
    print("✅ All tests completed successfully!")
    print("🎉 Your Binance Futures API is working perfectly!")
    return True

if __name__ == "__main__":
    asyncio.run(test_futures_system())
