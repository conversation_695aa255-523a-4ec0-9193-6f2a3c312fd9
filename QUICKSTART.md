# ArbTrader Quick Start Guide

This guide will get you up and running with <PERSON><PERSON><PERSON><PERSON><PERSON> in under 10 minutes.

## 🚀 Quick Setup

### 1. Install Dependencies
```bash
# Install Python dependencies
pip install -r requirements.txt

# Install TA-Lib (required)
# macOS:
brew install ta-lib

# Ubuntu/Debian:
sudo apt-get install libta-lib-dev

# Windows: Download from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
```

### 2. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit with your settings
nano .env
```

**Minimum required settings:**
```env
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
BINANCE_TESTNET=true
TRADING_MODE=paper
```

### 3. Test Configuration
```bash
python -m arbtrader.cli config-check
```

### 4. Start Paper Trading
```bash
python -m arbtrader.cli start --paper
```

## 📊 What You'll See

The system will display a live dashboard showing:
- Portfolio status
- Active strategies
- Real-time performance
- Risk metrics

## 🎯 Key Strategies

### Triangular Arbitrage
- Finds price differences across 3 currency pairs
- Executes simultaneous trades for profit
- Typical profit: 0.1-0.5% per opportunity

### Pairs Trading
- Trades correlated cryptocurrency pairs
- Uses statistical mean reversion
- Typical holding period: 1-24 hours

### Basis Trading
- Captures spreads between spot and futures
- Market-neutral strategy
- Profits from basis convergence

## ⚙️ Essential Settings

### Risk Management
```env
MAX_POSITION_SIZE=1000      # Max $1000 per position
RISK_PER_TRADE=0.02        # 2% risk per trade
MAX_DAILY_LOSS=0.05        # 5% daily loss limit
```

### Strategy Thresholds
```env
MIN_PROFIT_THRESHOLD=0.001  # 0.1% minimum profit
CORRELATION_THRESHOLD=0.8   # 80% correlation for pairs
ZSCORE_ENTRY=2.0           # Entry signal threshold
```

## 🛡️ Safety First

### Before Live Trading
1. ✅ Test thoroughly in paper mode
2. ✅ Verify API permissions
3. ✅ Start with small amounts
4. ✅ Monitor closely for first few hours
5. ✅ Have emergency stop plan

### Risk Controls
- Automatic daily loss limits
- Position size restrictions
- Real-time risk monitoring
- Emergency stop functionality

## 📈 Performance Monitoring

### Live Dashboard
Press `Ctrl+C` to stop trading safely.

### Log Files
- `logs/arbtrader.log` - General logs
- `logs/trading.log` - Trade execution
- `logs/errors.log` - Error tracking

### Key Metrics
- Total P&L
- Win rate
- Sharpe ratio
- Maximum drawdown

## 🔧 Common Commands

```bash
# Check configuration
python -m arbtrader.cli config-check

# Start paper trading
python -m arbtrader.cli start --paper

# Start live trading
python -m arbtrader.cli start --live

# Check status
python -m arbtrader.cli status

# Run tests
pytest tests/

# Format code
make format

# Install in development mode
pip install -e .
```

## 🚨 Troubleshooting

### API Connection Issues
```bash
# Check API keys
python -m arbtrader.cli config-check

# Verify network connectivity
ping api.binance.com
```

### No Opportunities Found
- Market conditions may not be favorable
- Adjust profit thresholds
- Check enabled strategies
- Verify symbol availability

### High Risk Warnings
- Reduce position sizes
- Check correlation limits
- Review daily P&L
- Consider stopping trading

## 📞 Getting Help

1. Check logs for error messages
2. Verify configuration settings
3. Test API connectivity
4. Review documentation

## 🎯 Next Steps

1. **Paper Trade**: Run for at least 24 hours
2. **Analyze Results**: Review performance metrics
3. **Optimize Settings**: Adjust based on results
4. **Start Small**: Begin with minimal live capital
5. **Scale Gradually**: Increase size as confidence grows

## ⚠️ Important Reminders

- **Start with paper trading**
- **Never risk more than you can afford to lose**
- **Monitor the system actively**
- **Have emergency procedures ready**
- **Keep API keys secure**

---

**Ready to start? Run:**
```bash
python -m arbtrader.cli start --paper
```

**Happy Trading! 🚀**
