#!/usr/bin/env python3
"""
Test script for the updated portfolio system with futures.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.portfolio import PortfolioManager
from arbtrader.core.config import get_config

async def test_portfolio_initialization():
    """Test portfolio manager initialization with futures."""
    print("🚀 Testing Portfolio Manager with Futures")
    print("=" * 50)
    
    config = get_config()
    print(f"Trading Mode: {config.trading_mode}")
    print(f"Testnet: {config.binance_testnet}")
    print()
    
    # Create portfolio manager
    portfolio_manager = PortfolioManager()
    
    print("🔌 Initializing Portfolio Manager...")
    try:
        success = await portfolio_manager.initialize()
        if success:
            print("✅ Portfolio Manager initialized successfully!")
            
            # Get status
            status = await portfolio_manager.get_portfolio_status()
            print(f"✅ Portfolio Value: ${status.total_value:.2f}")
            print(f"✅ Active Strategies: {status.active_strategies}")
            print(f"✅ Risk Level: {status.risk_level}")
            
        else:
            print("❌ Failed to initialize Portfolio Manager")
            return False
            
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        return False
    finally:
        # Clean up
        await portfolio_manager.stop_trading()
    
    print("✅ Portfolio test completed successfully!")
    return True

if __name__ == "__main__":
    asyncio.run(test_portfolio_initialization())
