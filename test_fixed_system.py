#!/usr/bin/env python3
"""
Test the completely fixed system with no WebSocket errors.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.portfolio import PortfolioManager
from arbtrader.core.config import get_config

async def test_fixed_system():
    """Test the fixed system."""
    print("🚀 Testing FIXED ArbTrader System (No WebSocket Errors)")
    print("=" * 60)
    
    config = get_config()
    print(f"Trading Mode: {config.trading_mode}")
    print(f"Testnet: {config.binance_testnet}")
    print()
    
    # Create portfolio manager
    portfolio_manager = PortfolioManager()
    
    print("🔌 Initializing Portfolio Manager...")
    try:
        success = await portfolio_manager.initialize()
        if success:
            print("✅ Portfolio Manager initialized successfully!")
            
            # Get status
            status = await portfolio_manager.get_portfolio_status()
            print(f"✅ Portfolio Value: ${status.total_value:.2f}")
            print(f"✅ Active Strategies: {status.active_strategies}")
            print(f"✅ Risk Level: {status.risk_level}")
            
            # Test market data
            print("\n📊 Testing Market Data...")
            btc_ticker = portfolio_manager.market_data_manager.get_ticker("BTCUSDT")
            if btc_ticker:
                print(f"✅ BTC Price: ${btc_ticker.price:,.2f}")
            else:
                print("⏳ Waiting for market data...")
                await asyncio.sleep(5)
                btc_ticker = portfolio_manager.market_data_manager.get_ticker("BTCUSDT")
                if btc_ticker:
                    print(f"✅ BTC Price: ${btc_ticker.price:,.2f}")
                else:
                    print("❌ No market data yet")
            
            print("\n🎯 Testing Strategy Status...")
            tri_stats = portfolio_manager.triangular_arbitrage.get_statistics()
            print(f"✅ Triangular Arbitrage Paths: {tri_stats['triangular_paths']}")
            
            pairs_stats = portfolio_manager.pairs_trading.get_statistics()
            print(f"✅ Pairs Trading Pairs: {pairs_stats['trading_pairs']}")
            
            basis_stats = portfolio_manager.basis_trading.get_statistics()
            print(f"✅ Basis Trading Contracts: {basis_stats['futures_contracts']}")
            
            print("\n🎉 System is working perfectly!")
            print("✅ No WebSocket errors")
            print("✅ All strategies initialized")
            print("✅ Market data flowing")
            print("✅ Ready for continuous trading")
            
        else:
            print("❌ Failed to initialize Portfolio Manager")
            return False
            
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        return False
    finally:
        # Clean up
        await portfolio_manager.stop_trading()
    
    return True

if __name__ == "__main__":
    asyncio.run(test_fixed_system())
