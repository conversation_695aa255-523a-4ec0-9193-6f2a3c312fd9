#!/usr/bin/env python3
"""
REAL TESTNET TRADER
Actually executes trades on Binance Testnet
REAL ORDERS - REAL RESULTS!
"""

import asyncio
import time
import sys
import os
import numpy as np
from collections import deque
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.futures_exchange import FuturesExchangeManager
from arbtrader.core.config import get_config

class RealTestnetTrader:
    """Real testnet trader - ACTUAL Binance orders!"""
    
    def __init__(self, starting_balance=200):
        self.exchange = FuturesExchangeManager()
        self.config = get_config()
        
        # Force LIVE trading mode (not paper)
        self.config.trading_mode = "live"
        
        # Adaptive settings based on balance
        self.starting_balance = starting_balance
        self.current_balance = starting_balance
        
        # Scale settings based on balance
        balance_ratio = starting_balance / 15000.0  # Scale from 15k baseline
        
        self.profit_target = 0.002  # 0.2% profit target
        self.stop_loss = 0.001  # 0.1% stop loss
        self.check_interval = 0.1  # 100ms checks
        self.deviation_threshold = 0.001  # 0.1% deviation
        
        # Position sizing based on balance
        self.max_position_pct = 0.2  # 20% of balance max
        self.min_position_value = 50  # Minimum $50 position (higher for real trading)
        
        # Trading stats
        self.total_profit = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
        # Price tracking
        self.price_history = {}
        self.moving_averages = {}
        self.active_positions = {}
        
        print("🔥 REAL TESTNET TRADER")
        print(f"💰 Starting balance: ${self.starting_balance:,.2f}")
        print(f"🎯 Profit target: {self.profit_target*100}%")
        print(f"🛡️ Stop loss: {self.stop_loss*100}%")
        print(f"📊 Max position: {self.max_position_pct*100}% of balance")
        print("🚀 REAL ORDERS ON BINANCE TESTNET!")
    
    async def initialize(self):
        """Initialize the real testnet trader."""
        print("🔌 Connecting to Binance Futures TESTNET...")
        
        if not await self.exchange.initialize():
            print("❌ Failed to connect to Binance testnet")
            return False
        
        print("✅ Connected to Binance Futures TESTNET")
        
        # Get real account balance
        try:
            balance_info = await self.exchange.get_futures_account_balance()
            if balance_info:
                usdt_balance = next((b for b in balance_info if b['asset'] == 'USDT'), None)
                if usdt_balance:
                    real_balance = float(usdt_balance['balance'])
                    print(f"💰 Real testnet balance: ${real_balance:,.2f} USDT")
                    self.current_balance = real_balance
                    self.starting_balance = real_balance
        except Exception as e:
            print(f"⚠️ Could not get real balance: {e}")
        
        # Focus on most liquid pairs for real trading
        self.target_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        
        print(f"📊 Trading {len(self.target_symbols)} pairs with REAL orders")
        
        # Initialize tracking
        for symbol in self.target_symbols:
            self.price_history[symbol] = deque(maxlen=100)
            self.moving_averages[symbol] = {'sma_10': 0, 'sma_20': 0}
        
        # Collect initial data
        print("📈 Collecting initial market data...")
        for _ in range(30):
            all_prices = await self.exchange.get_all_futures_tickers()
            for symbol in self.target_symbols:
                if symbol in all_prices:
                    self.price_history[symbol].append(all_prices[symbol])
            await asyncio.sleep(0.2)
        
        # Calculate initial moving averages
        for symbol in self.target_symbols:
            await self._update_moving_averages(symbol)
        
        print("🚀 Ready for REAL testnet trading!")
        return True
    
    async def _update_moving_averages(self, symbol):
        """Update moving averages."""
        if len(self.price_history[symbol]) < 20:
            return
        
        prices = list(self.price_history[symbol])
        sma_10 = np.mean(prices[-10:]) if len(prices) >= 10 else prices[-1]
        sma_20 = np.mean(prices[-20:]) if len(prices) >= 20 else prices[-1]
        
        self.moving_averages[symbol] = {'sma_10': sma_10, 'sma_20': sma_20}
    
    async def start_real_trading(self):
        """Start real testnet trading."""
        print("🔥 Starting REAL testnet trading...")
        print("💰 Executing ACTUAL orders on Binance testnet!")
        print("📊 Check your Binance testnet futures account for real orders!")
        
        check_count = 0
        
        while True:
            try:
                start_time = time.time()
                check_count += 1
                
                # Display status every 10 seconds
                if check_count % 100 == 0:
                    await self._display_real_status()
                
                # Get current prices
                all_prices = await self.exchange.get_all_futures_tickers()
                
                # Check for real trading opportunities
                for symbol in self.target_symbols:
                    if symbol in all_prices:
                        current_price = all_prices[symbol]
                        await self._check_real_opportunity(symbol, current_price)
                
                # Manage real positions
                await self._manage_real_positions(all_prices)
                
                # Update account balance periodically
                if check_count % 500 == 0:  # Every 50 seconds
                    await self._update_real_balance()
                
                scan_time = time.time() - start_time
                sleep_time = max(0, self.check_interval - scan_time)
                await asyncio.sleep(sleep_time)
                
            except Exception as e:
                print(f"❌ Error in real trading: {e}")
                await asyncio.sleep(1)
    
    async def _display_real_status(self):
        """Display real trading status."""
        profit_pct = (self.total_profit / self.starting_balance) * 100 if self.starting_balance > 0 else 0
        win_rate = (self.winning_trades / max(self.total_trades, 1)) * 100
        
        print(f"\n🔥 REAL TESTNET STATUS:")
        print(f"💰 Balance: ${self.current_balance:,.2f} USDT")
        print(f"📈 Profit/Loss: ${self.total_profit:+.4f} ({profit_pct:+.3f}%)")
        print(f"🎯 Trades: {self.total_trades} | Win Rate: {win_rate:.1f}%")
        print(f"📊 Active Positions: {len(self.active_positions)}")
        print("🔍 Check your Binance testnet for real orders!")
    
    async def _update_real_balance(self):
        """Update real account balance from Binance."""
        try:
            balance_info = await self.exchange.get_futures_account_balance()
            if balance_info:
                usdt_balance = next((b for b in balance_info if b['asset'] == 'USDT'), None)
                if usdt_balance:
                    new_balance = float(usdt_balance['balance'])
                    balance_change = new_balance - self.current_balance
                    if abs(balance_change) > 0.01:  # Significant change
                        print(f"💰 Balance updated: ${self.current_balance:.2f} → ${new_balance:.2f} (${balance_change:+.2f})")
                        self.current_balance = new_balance
        except Exception as e:
            print(f"⚠️ Could not update balance: {e}")
    
    async def _check_real_opportunity(self, symbol, current_price):
        """Check for real trading opportunities."""
        # Update price history
        self.price_history[symbol].append(current_price)
        await self._update_moving_averages(symbol)
        
        if len(self.price_history[symbol]) < 20:
            return
        
        # Check if we already have a position
        existing_positions = [k for k in self.active_positions.keys() if symbol in k]
        if existing_positions:
            return
        
        # Get moving average
        sma_10 = self.moving_averages[symbol]['sma_10']
        if sma_10 == 0:
            return
        
        # Calculate deviation
        deviation = (current_price - sma_10) / sma_10
        
        # Look for opportunities
        if abs(deviation) >= self.deviation_threshold:
            # Calculate position size based on current balance
            max_position_value = self.current_balance * self.max_position_pct
            position_value = max(self.min_position_value, max_position_value)
            position_value = min(position_value, self.current_balance * 0.3)  # Max 30% of balance

            # Ensure minimum position size for real trading
            if position_value >= self.min_position_value and self.current_balance >= 100:
                if deviation < -self.deviation_threshold:
                    # Price below average - BUY
                    await self._execute_real_trade(symbol, 'buy', current_price, position_value, deviation)
                elif deviation > self.deviation_threshold:
                    # Price above average - SELL
                    await self._execute_real_trade(symbol, 'sell', current_price, position_value, deviation)
    
    async def _execute_real_trade(self, symbol, side, entry_price, position_value, deviation):
        """Execute a REAL trade on Binance testnet."""
        print(f"\n🔥 REAL OPPORTUNITY: {symbol} {side.upper()}")
        print(f"📊 Deviation: {deviation*100:.3f}% from SMA10")
        print(f"💰 Position size: ${position_value:.2f}")
        
        quantity = position_value / entry_price

        # Round quantity to proper precision for Binance
        if symbol == 'BTCUSDT':
            quantity = round(quantity, 3)  # 3 decimal places for BTC
        elif symbol == 'ETHUSDT':
            quantity = round(quantity, 2)  # 2 decimal places for ETH
        else:
            quantity = round(quantity, 1)  # 1 decimal place for others

        # Ensure minimum quantity
        min_quantities = {'BTCUSDT': 0.001, 'ETHUSDT': 0.01, 'BNBUSDT': 0.1}
        min_qty = min_quantities.get(symbol, 0.1)
        if quantity < min_qty:
            print(f"⚠️ Quantity {quantity} too small for {symbol}, minimum is {min_qty}")
            return
        
        # Calculate targets
        if side == 'buy':
            profit_target = entry_price * (1 + self.profit_target)
            stop_loss = entry_price * (1 - self.stop_loss)
        else:
            profit_target = entry_price * (1 - self.profit_target)
            stop_loss = entry_price * (1 + self.stop_loss)
        
        print(f"🎯 Executing REAL order on Binance testnet...")
        
        # Execute REAL trade
        trade = await self.exchange.place_futures_market_order(symbol, side, quantity)
        
        if trade:
            self.total_trades += 1
            
            # Store position
            position_key = f"{symbol}_{int(time.time()*1000)}"
            self.active_positions[position_key] = {
                'symbol': symbol,
                'side': side,
                'entry_price': trade.price,
                'quantity': trade.quantity,
                'profit_target': profit_target,
                'stop_loss': stop_loss,
                'entry_time': time.time(),
                'order_id': trade.order_id
            }
            
            print(f"✅ REAL TRADE EXECUTED!")
            print(f"   Order ID: {trade.order_id}")
            print(f"   {side.upper()} {trade.quantity:.6f} {symbol} @ ${trade.price:.4f}")
            print(f"   🎯 Target: ${profit_target:.4f} | 🛡️ Stop: ${stop_loss:.4f}")
            print(f"   💡 Check your Binance testnet futures account!")
            
        else:
            print(f"❌ Failed to execute REAL trade for {symbol}")
    
    async def _manage_real_positions(self, current_prices):
        """Manage real positions."""
        positions_to_close = []
        
        for position_key, position in self.active_positions.items():
            symbol = position['symbol']
            if symbol not in current_prices:
                continue
            
            current_price = current_prices[symbol]
            entry_price = position['entry_price']
            side = position['side']
            
            # Calculate P&L
            if side == 'buy':
                pnl = (current_price - entry_price) * position['quantity']
                pnl_pct = (current_price - entry_price) / entry_price
            else:
                pnl = (entry_price - current_price) * position['quantity']
                pnl_pct = (entry_price - current_price) / entry_price
            
            # Check exit conditions
            profit_hit = False
            stop_hit = False
            
            if side == 'buy':
                profit_hit = current_price >= position['profit_target']
                stop_hit = current_price <= position['stop_loss']
            else:
                profit_hit = current_price <= position['profit_target']
                stop_hit = current_price >= position['stop_loss']
            
            # Time-based exit (5 minutes max)
            time_exit = time.time() - position['entry_time'] > 300
            
            if profit_hit or stop_hit or time_exit:
                exit_reason = "PROFIT" if profit_hit else "STOP" if stop_hit else "TIME"
                positions_to_close.append((position_key, position, current_price, pnl, pnl_pct, exit_reason))
        
        # Close positions
        for position_key, position, exit_price, pnl, pnl_pct, exit_reason in positions_to_close:
            await self._close_real_position(position_key, position, exit_price, pnl, pnl_pct, exit_reason)
    
    async def _close_real_position(self, position_key, position, exit_price, pnl, pnl_pct, exit_reason):
        """Close a real position."""
        symbol = position['symbol']
        side = position['side']
        close_side = 'sell' if side == 'buy' else 'buy'
        
        print(f"\n🔥 CLOSING REAL POSITION: {symbol} ({exit_reason})")
        
        # Execute REAL closing trade
        close_trade = await self.exchange.place_futures_market_order(
            symbol, close_side, position['quantity']
        )
        
        if close_trade:
            # Calculate actual P&L
            actual_pnl = pnl  # Simplified - fees already included in trade execution
            self.total_profit += actual_pnl
            
            if actual_pnl > 0:
                self.winning_trades += 1
                status_emoji = "💰"
            else:
                self.losing_trades += 1
                status_emoji = "💔"
            
            hold_time = time.time() - position['entry_time']
            
            print(f"✅ REAL POSITION CLOSED!")
            print(f"   Close Order ID: {close_trade.order_id}")
            print(f"   {status_emoji} P&L: ${actual_pnl:+.4f} ({pnl_pct*100:+.3f}%)")
            print(f"   ⏱️ Hold time: {hold_time:.1f}s")
            print(f"   📊 Total profit: ${self.total_profit:+.4f}")
            print(f"   💡 Check your Binance testnet for real order!")
            
            del self.active_positions[position_key]
            
            # Update balance
            await self._update_real_balance()

async def main():
    """Main function to run real testnet trading."""
    # Get starting balance from user
    try:
        balance_input = input("Enter your starting balance (default 200): ").strip()
        starting_balance = float(balance_input) if balance_input else 200.0
    except:
        starting_balance = 200.0
    
    trader = RealTestnetTrader(starting_balance)
    
    if await trader.initialize():
        print("🔥 Starting REAL testnet trading system...")
        print("💰 ACTUAL orders will be placed on Binance testnet!")
        print("📊 Check your Binance testnet futures account!")
        print("⚡ Press Ctrl+C to stop")
        
        try:
            await trader.start_real_trading()
        except KeyboardInterrupt:
            print("\n🛑 Stopping real testnet trader...")
            
            # Final statistics
            profit_pct = (trader.total_profit / trader.starting_balance) * 100
            win_rate = (trader.winning_trades / max(trader.total_trades, 1)) * 100
            
            print(f"\n🔥 FINAL REAL RESULTS:")
            print(f"💰 Final Balance: ${trader.current_balance:,.2f}")
            print(f"📈 Total Profit: ${trader.total_profit:+.4f} ({profit_pct:+.3f}%)")
            print(f"🎯 Total Trades: {trader.total_trades}")
            print(f"✅ Winning Trades: {trader.winning_trades}")
            print(f"❌ Losing Trades: {trader.losing_trades}")
            print(f"🏆 Win Rate: {win_rate:.1f}%")
    else:
        print("❌ Failed to initialize real testnet trader")

if __name__ == "__main__":
    asyncio.run(main())
