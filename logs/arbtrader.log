2025-07-19 00:35:59 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: 'NoneType' object has no attribute 'binance_api_key'
2025-07-19 00:37:28 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:42:41 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:44:22 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:44:41 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:50:30 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:54:59 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 00:54:59 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 00:55:01 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.001 BTCUSDT @ 117973.69002000001
2025-07-19 00:55:38 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 00:55:38 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 00:56:03 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 00:56:03 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 00:56:03 | INFO     | arbtrader.core.futures_exchange:close:301 | Futures exchange connections closed
2025-07-19 00:57:23 | INFO     | arbtrader.core.portfolio:initialize:69 | Initializing ArbTrader Portfolio Manager...
2025-07-19 00:57:25 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:57:25 | ERROR    | arbtrader.core.portfolio:initialize:73 | Failed to initialize exchange manager
2025-07-19 00:57:25 | INFO     | arbtrader.core.portfolio:stop_trading:281 | Stopping trading activities...
2025-07-19 00:57:25 | INFO     | arbtrader.strategies.triangular_arbitrage:stop_monitoring:320 | Triangular arbitrage monitoring stopped
2025-07-19 00:57:25 | INFO     | arbtrader.strategies.pairs_trading:stop_monitoring:464 | Pairs trading monitoring stopped
2025-07-19 00:57:25 | INFO     | arbtrader.strategies.basis_trading:stop_monitoring:453 | Basis trading monitoring stopped
2025-07-19 00:57:25 | INFO     | arbtrader.core.portfolio:stop_trading:295 | All trading activities stopped
2025-07-19 00:57:55 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 00:57:55 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 00:57:55 | INFO     | arbtrader.core.futures_exchange:close:301 | Futures exchange connections closed
2025-07-19 01:06:12 | INFO     | arbtrader.core.portfolio:initialize:69 | Initializing ArbTrader Portfolio Manager...
2025-07-19 01:06:14 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 01:06:14 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 01:06:14 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize portfolio manager: 'permissions'
2025-07-19 01:06:14 | INFO     | arbtrader.core.portfolio:stop_trading:281 | Stopping trading activities...
2025-07-19 01:06:14 | INFO     | arbtrader.strategies.triangular_arbitrage:stop_monitoring:320 | Triangular arbitrage monitoring stopped
2025-07-19 01:06:14 | INFO     | arbtrader.strategies.pairs_trading:stop_monitoring:464 | Pairs trading monitoring stopped
2025-07-19 01:06:14 | INFO     | arbtrader.strategies.basis_trading:stop_monitoring:453 | Basis trading monitoring stopped
2025-07-19 01:06:14 | INFO     | arbtrader.core.portfolio:stop_trading:295 | All trading activities stopped
2025-07-19 01:06:42 | INFO     | arbtrader.core.portfolio:initialize:69 | Initializing ArbTrader Portfolio Manager...
2025-07-19 01:06:44 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 01:06:44 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 01:06:44 | INFO     | arbtrader.core.portfolio:initialize:92 | Selected 100 major trading symbols
2025-07-19 01:06:46 | ERROR    | arbtrader.core.logger:log_error:86 | Error in ticker stream: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:46 | INFO     | arbtrader.data.market_data:start:80 | Market data collection started for 100 symbols
2025-07-19 01:06:46 | INFO     | arbtrader.core.portfolio:initialize:100 | Waiting for initial market data...
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for XRPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for TRBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ZECUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for LTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BNBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for NEOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for AVAXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for THETAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ICXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ADAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOSTUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DEFIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for YFIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BANDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ZILUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for EGLDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for XLMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for COMPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:51 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize triangular arbitrage engine: 'permissions'
2025-07-19 01:06:51 | WARNING  | arbtrader.core.portfolio:initialize:109 | Failed to initialize triangular arbitrage engine
2025-07-19 01:06:51 | INFO     | arbtrader.strategies.pairs_trading:_find_correlated_pairs:106 | Analyzing correlations between trading pairs...
2025-07-19 01:06:51 | INFO     | arbtrader.strategies.pairs_trading:initialize:97 | Found 0 correlated pairs for trading
2025-07-19 01:06:51 | INFO     | arbtrader.core.portfolio:initialize:113 | Pairs trading engine initialized
2025-07-19 01:06:51 | INFO     | arbtrader.strategies.basis_trading:_load_futures_contracts:126 | Loaded perpetual futures contracts
2025-07-19 01:06:51 | INFO     | arbtrader.strategies.basis_trading:initialize:93 | Loaded 10 futures contracts
2025-07-19 01:06:51 | INFO     | arbtrader.core.portfolio:initialize:120 | Basis trading engine initialized
2025-07-19 01:06:51 | INFO     | arbtrader.core.portfolio:initialize:129 | Portfolio manager initialized with 2 active strategies
2025-07-19 01:06:51 | INFO     | arbtrader.core.portfolio:stop_trading:281 | Stopping trading activities...
2025-07-19 01:06:51 | INFO     | arbtrader.strategies.triangular_arbitrage:stop_monitoring:320 | Triangular arbitrage monitoring stopped
2025-07-19 01:06:51 | INFO     | arbtrader.strategies.pairs_trading:stop_monitoring:464 | Pairs trading monitoring stopped
2025-07-19 01:06:51 | INFO     | arbtrader.strategies.basis_trading:stop_monitoring:453 | Basis trading monitoring stopped
2025-07-19 01:06:51 | INFO     | arbtrader.core.portfolio:stop_trading:295 | All trading activities stopped
2025-07-19 01:07:21 | INFO     | arbtrader.core.portfolio:initialize:69 | Initializing ArbTrader Portfolio Manager...
2025-07-19 01:07:25 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 01:07:25 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 01:07:25 | INFO     | arbtrader.core.portfolio:initialize:92 | Selected 100 major trading symbols
2025-07-19 01:07:27 | ERROR    | arbtrader.core.logger:log_error:86 | Error in ticker stream: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:27 | INFO     | arbtrader.data.market_data:start:80 | Market data collection started for 100 symbols
2025-07-19 01:07:27 | INFO     | arbtrader.core.portfolio:initialize:100 | Waiting for initial market data...
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for RUNEUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ALGOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BANDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOSTUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for SUSHIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BNBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for TRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for NEOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BALUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for OMGUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ONTUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:30 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DOGEUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:30 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for MKRUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for VETUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for EGLDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for UNIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for NEOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for SNXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:32 | INFO     | arbtrader.strategies.triangular_arbitrage:initialize:65 | Generated 6 triangular arbitrage paths
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:initialize:106 | Triangular arbitrage engine initialized
2025-07-19 01:07:32 | INFO     | arbtrader.strategies.pairs_trading:_find_correlated_pairs:106 | Analyzing correlations between trading pairs...
2025-07-19 01:07:32 | INFO     | arbtrader.strategies.pairs_trading:initialize:97 | Found 0 correlated pairs for trading
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:initialize:113 | Pairs trading engine initialized
2025-07-19 01:07:32 | INFO     | arbtrader.strategies.basis_trading:_load_futures_contracts:126 | Loaded perpetual futures contracts
2025-07-19 01:07:32 | INFO     | arbtrader.strategies.basis_trading:initialize:93 | Loaded 10 futures contracts
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:initialize:120 | Basis trading engine initialized
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:initialize:129 | Portfolio manager initialized with 3 active strategies
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:stop_trading:281 | Stopping trading activities...
2025-07-19 01:07:32 | INFO     | arbtrader.strategies.triangular_arbitrage:stop_monitoring:320 | Triangular arbitrage monitoring stopped
2025-07-19 01:07:32 | INFO     | arbtrader.strategies.pairs_trading:stop_monitoring:464 | Pairs trading monitoring stopped
2025-07-19 01:07:32 | INFO     | arbtrader.strategies.basis_trading:stop_monitoring:453 | Basis trading monitoring stopped
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:stop_trading:295 | All trading activities stopped
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:start_trading:145 | Starting ArbTrader trading engine...
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:start_trading:146 | Trading mode: paper
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:start_trading:147 | Active strategies: 3
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:start_trading:155 | Triangular arbitrage monitoring started
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:start_trading:160 | Pairs trading monitoring started
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:start_trading:165 | Basis trading monitoring started
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:start_trading:178 | Trading tasks cancelled
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:_shutdown:299 | Shutting down ArbTrader...
2025-07-19 01:07:32 | INFO     | arbtrader.data.market_data:stop:304 | Market data collection stopped
2025-07-19 01:07:32 | INFO     | arbtrader.core.futures_exchange:close:301 | Futures exchange connections closed
2025-07-19 01:07:32 | INFO     | arbtrader.core.portfolio:_shutdown:308 | ArbTrader shutdown complete
2025-07-19 01:10:33 | INFO     | arbtrader.core.portfolio:initialize:69 | Initializing ArbTrader Portfolio Manager...
2025-07-19 01:10:36 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 01:10:36 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 01:10:36 | INFO     | arbtrader.core.portfolio:initialize:92 | Selected 100 major trading symbols
2025-07-19 01:10:37 | ERROR    | arbtrader.core.logger:log_error:86 | Error in ticker stream: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:37 | INFO     | arbtrader.data.market_data:start:80 | Market data collection started for 100 symbols
2025-07-19 01:10:37 | INFO     | arbtrader.core.portfolio:initialize:100 | Waiting for initial market data...
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ZECUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for KNCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for XLMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for AVAXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for XTZUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for TRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ADAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for TRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BANDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for CRVUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for SXPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for QTUMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DOGEUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BALUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ZRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ADAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for MKRUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for UNIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BNBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for XLMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for KAVAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for COMPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | INFO     | arbtrader.strategies.triangular_arbitrage:initialize:65 | Generated 6 triangular arbitrage paths
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:initialize:106 | Triangular arbitrage engine initialized
2025-07-19 01:10:42 | INFO     | arbtrader.strategies.pairs_trading:_find_correlated_pairs:106 | Analyzing correlations between trading pairs...
2025-07-19 01:10:42 | INFO     | arbtrader.strategies.pairs_trading:initialize:97 | Found 0 correlated pairs for trading
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:initialize:113 | Pairs trading engine initialized
2025-07-19 01:10:42 | INFO     | arbtrader.strategies.basis_trading:_load_futures_contracts:126 | Loaded perpetual futures contracts
2025-07-19 01:10:42 | INFO     | arbtrader.strategies.basis_trading:initialize:93 | Loaded 10 futures contracts
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:initialize:120 | Basis trading engine initialized
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:initialize:129 | Portfolio manager initialized with 3 active strategies
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:stop_trading:281 | Stopping trading activities...
2025-07-19 01:10:42 | INFO     | arbtrader.strategies.triangular_arbitrage:stop_monitoring:320 | Triangular arbitrage monitoring stopped
2025-07-19 01:10:42 | INFO     | arbtrader.strategies.pairs_trading:stop_monitoring:464 | Pairs trading monitoring stopped
2025-07-19 01:10:42 | INFO     | arbtrader.strategies.basis_trading:stop_monitoring:453 | Basis trading monitoring stopped
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:stop_trading:295 | All trading activities stopped
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:start_trading:145 | Starting ArbTrader trading engine...
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:start_trading:146 | Trading mode: paper
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:start_trading:147 | Active strategies: 3
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:start_trading:155 | Triangular arbitrage monitoring started
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:start_trading:160 | Pairs trading monitoring started
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:start_trading:165 | Basis trading monitoring started
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:start_trading:178 | Trading tasks cancelled
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:_shutdown:299 | Shutting down ArbTrader...
2025-07-19 01:10:42 | INFO     | arbtrader.data.market_data:stop:304 | Market data collection stopped
2025-07-19 01:10:42 | INFO     | arbtrader.core.futures_exchange:close:301 | Futures exchange connections closed
2025-07-19 01:10:42 | INFO     | arbtrader.core.portfolio:_shutdown:308 | ArbTrader shutdown complete
2025-07-19 01:18:55 | INFO     | arbtrader.core.portfolio:initialize:69 | Initializing ArbTrader Portfolio Manager...
2025-07-19 01:18:58 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 01:18:58 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 01:18:58 | INFO     | arbtrader.core.portfolio:initialize:92 | Selected 100 major trading symbols
2025-07-19 01:18:58 | INFO     | arbtrader.data.market_data_simple:start:70 | Market data collection started for 100 symbols (REST mode)
2025-07-19 01:18:58 | INFO     | arbtrader.core.portfolio:initialize:100 | Waiting for initial market data...
2025-07-19 01:19:03 | INFO     | arbtrader.strategies.triangular_arbitrage:initialize:65 | Generated 6 triangular arbitrage paths
2025-07-19 01:19:03 | INFO     | arbtrader.core.portfolio:initialize:106 | Triangular arbitrage engine initialized
2025-07-19 01:19:03 | INFO     | arbtrader.strategies.pairs_trading:_find_correlated_pairs:106 | Analyzing correlations between trading pairs...
2025-07-19 01:19:03 | INFO     | arbtrader.strategies.pairs_trading:initialize:97 | Found 0 correlated pairs for trading
2025-07-19 01:19:03 | INFO     | arbtrader.core.portfolio:initialize:113 | Pairs trading engine initialized
2025-07-19 01:19:03 | INFO     | arbtrader.strategies.basis_trading:_load_futures_contracts:126 | Loaded perpetual futures contracts
2025-07-19 01:19:03 | INFO     | arbtrader.strategies.basis_trading:initialize:93 | Loaded 10 futures contracts
2025-07-19 01:19:03 | INFO     | arbtrader.core.portfolio:initialize:120 | Basis trading engine initialized
2025-07-19 01:19:03 | INFO     | arbtrader.core.portfolio:initialize:129 | Portfolio manager initialized with 3 active strategies
2025-07-19 01:19:03 | INFO     | arbtrader.core.portfolio:stop_trading:281 | Stopping trading activities...
2025-07-19 01:19:03 | INFO     | arbtrader.strategies.triangular_arbitrage:stop_monitoring:320 | Triangular arbitrage monitoring stopped
2025-07-19 01:19:03 | INFO     | arbtrader.strategies.pairs_trading:stop_monitoring:464 | Pairs trading monitoring stopped
2025-07-19 01:19:03 | INFO     | arbtrader.strategies.basis_trading:stop_monitoring:453 | Basis trading monitoring stopped
2025-07-19 01:19:03 | INFO     | arbtrader.core.portfolio:stop_trading:295 | All trading activities stopped
2025-07-19 01:20:55 | INFO     | arbtrader.core.portfolio:initialize:69 | Initializing ArbTrader Portfolio Manager...
2025-07-19 01:20:57 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 01:20:57 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 01:20:57 | INFO     | arbtrader.core.portfolio:initialize:92 | Selected 100 major trading symbols
2025-07-19 01:20:57 | INFO     | arbtrader.data.market_data_simple:start:70 | Market data collection started for 100 symbols (REST mode)
2025-07-19 01:20:57 | INFO     | arbtrader.core.portfolio:initialize:100 | Waiting for initial market data...
2025-07-19 01:21:02 | INFO     | arbtrader.strategies.triangular_arbitrage:initialize:65 | Generated 6 triangular arbitrage paths
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:initialize:106 | Triangular arbitrage engine initialized
2025-07-19 01:21:02 | INFO     | arbtrader.strategies.pairs_trading:_find_correlated_pairs:106 | Analyzing correlations between trading pairs...
2025-07-19 01:21:02 | INFO     | arbtrader.strategies.pairs_trading:initialize:97 | Found 0 correlated pairs for trading
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:initialize:113 | Pairs trading engine initialized
2025-07-19 01:21:02 | INFO     | arbtrader.strategies.basis_trading:_load_futures_contracts:126 | Loaded perpetual futures contracts
2025-07-19 01:21:02 | INFO     | arbtrader.strategies.basis_trading:initialize:93 | Loaded 10 futures contracts
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:initialize:120 | Basis trading engine initialized
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:initialize:129 | Portfolio manager initialized with 3 active strategies
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:start_trading:145 | Starting ArbTrader trading engine...
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:start_trading:146 | Trading mode: paper
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:start_trading:147 | Active strategies: 3
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:start_trading:155 | Triangular arbitrage monitoring started
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:start_trading:160 | Pairs trading monitoring started
2025-07-19 01:21:02 | INFO     | arbtrader.core.portfolio:start_trading:165 | Basis trading monitoring started
2025-07-19 01:21:02 | INFO     | arbtrader.strategies.triangular_arbitrage:start_monitoring:149 | Starting triangular arbitrage monitoring
2025-07-19 01:21:02 | ERROR    | arbtrader.strategies.pairs_trading:start_monitoring:188 | No trading pairs available
2025-07-19 01:21:02 | INFO     | arbtrader.strategies.basis_trading:start_monitoring:138 | Starting basis trading monitoring
2025-07-19 01:21:02 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:12 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:22 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:32 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:52 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:22:03 | INFO     | arbtrader.core.portfolio:initialize:69 | Initializing ArbTrader Portfolio Manager...
2025-07-19 01:22:06 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 532 futures symbols
2025-07-19 01:22:06 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-19 01:22:06 | INFO     | arbtrader.core.portfolio:initialize:92 | Selected 100 major trading symbols
2025-07-19 01:22:06 | INFO     | arbtrader.data.market_data_simple:start:70 | Market data collection started for 100 symbols (REST mode)
2025-07-19 01:22:06 | INFO     | arbtrader.core.portfolio:initialize:100 | Waiting for initial market data...
2025-07-19 01:22:11 | INFO     | arbtrader.strategies.triangular_arbitrage:initialize:65 | Generated 6 triangular arbitrage paths
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:initialize:106 | Triangular arbitrage engine initialized
2025-07-19 01:22:11 | INFO     | arbtrader.strategies.pairs_trading:_find_correlated_pairs:106 | Analyzing correlations between trading pairs...
2025-07-19 01:22:11 | INFO     | arbtrader.strategies.pairs_trading:initialize:97 | Found 0 correlated pairs for trading
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:initialize:113 | Pairs trading engine initialized
2025-07-19 01:22:11 | INFO     | arbtrader.strategies.basis_trading:_load_futures_contracts:126 | Loaded perpetual futures contracts
2025-07-19 01:22:11 | INFO     | arbtrader.strategies.basis_trading:initialize:93 | Loaded 10 futures contracts
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:initialize:120 | Basis trading engine initialized
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:initialize:129 | Portfolio manager initialized with 3 active strategies
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:start_trading:145 | Starting ArbTrader trading engine...
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:start_trading:146 | Trading mode: paper
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:start_trading:147 | Active strategies: 3
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:start_trading:155 | Triangular arbitrage monitoring started
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:start_trading:160 | Pairs trading monitoring started
2025-07-19 01:22:11 | INFO     | arbtrader.core.portfolio:start_trading:165 | Basis trading monitoring started
2025-07-19 01:22:11 | INFO     | arbtrader.strategies.triangular_arbitrage:start_monitoring:149 | Starting triangular arbitrage monitoring
2025-07-19 01:22:11 | ERROR    | arbtrader.strategies.pairs_trading:start_monitoring:188 | No trading pairs available
2025-07-19 01:22:11 | INFO     | arbtrader.strategies.basis_trading:start_monitoring:138 | Starting basis trading monitoring
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:218 | === PORTFOLIO STATUS ===
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:219 | Portfolio Value: $15000.00
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:220 | Daily P&L: $0.00
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:221 | Total Trades: 0
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:222 | Active Strategies: 3
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:223 | Risk Level: low
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:224 | Uptime: 0.1 hours
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:229 | Triangular Arbitrage - Opportunities: 0, Executed: 0, Profit: $0.0000
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:235 | Pairs Trading - Signals: 0, Trades: 0, Profit: $0.0000
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:241 | Basis Trading - Opportunities: 0, Trades: 0, Profit: $0.0000
2025-07-19 01:27:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:245 | ========================
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:218 | === PORTFOLIO STATUS ===
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:219 | Portfolio Value: $15000.00
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:220 | Daily P&L: $0.00
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:221 | Total Trades: 0
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:222 | Active Strategies: 3
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:223 | Risk Level: low
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:224 | Uptime: 0.2 hours
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:229 | Triangular Arbitrage - Opportunities: 0, Executed: 0, Profit: $0.0000
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:235 | Pairs Trading - Signals: 0, Trades: 0, Profit: $0.0000
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:241 | Basis Trading - Opportunities: 0, Trades: 0, Profit: $0.0000
2025-07-19 01:32:12 | INFO     | arbtrader.core.portfolio:_status_reporting_loop:245 | ========================
2025-07-19 01:33:53 | INFO     | arbtrader.core.portfolio:_signal_handler:315 | Received signal 2, initiating graceful shutdown...
2025-07-21 13:58:50 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 13:58:50 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:00:33 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 14:00:33 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:00:39 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 27.56035718222908 ADAUSDT @ 0.90018
2025-07-21 14:00:45 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 27.77777777777778 ADAUSDT @ 0.8998200000000001
2025-07-21 14:00:46 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.0066536432689083234 ETHUSDT @ 3780.65598
2025-07-21 14:00:47 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.033635605306353095 BNBUSDT @ 743.111348
2025-07-21 14:00:52 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006754146370456824 ETHUSDT @ 3751.389572
2025-07-21 14:00:53 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.03289443386705649 BNBUSDT @ 760.05198
2025-07-21 14:01:04 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006613756613756613 ETHUSDT @ 3780.756
2025-07-21 14:01:07 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.03410036419188957 BNBUSDT @ 759.8549985999999
2025-07-21 14:01:10 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 27.56035718222908 ADAUSDT @ 0.8998200000000001
2025-07-21 14:01:12 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.03571428571428571 BNBUSDT @ 746.6392979999999
2025-07-21 14:01:17 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 27.77777777777778 ADAUSDT @ 0.90018
2025-07-21 14:01:17 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006789603758724641 ETHUSDT @ 3779.14402
2025-07-21 14:01:18 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.03289443386705649 BNBUSDT @ 760.1590014
2025-07-21 14:01:18 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.0066536432689083234 ETHUSDT @ 3681.36358
2025-07-21 14:01:20 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.033635605306353095 BNBUSDT @ 760.1590014
2025-07-21 14:01:22 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006754146370456824 ETHUSDT @ 3696.619176
2025-07-21 14:01:24 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.03289443386705649 BNBUSDT @ 759.8549985999999
2025-07-21 14:01:27 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006613931585491679 ETHUSDT @ 3689.2276979999997
2025-07-21 14:01:32 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.0067326828663993345 ETHUSDT @ 3687.752302
2025-07-21 14:01:36 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006613756613756613 ETHUSDT @ 3687.752302
2025-07-21 14:01:38 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.0066307193269554655 ETHUSDT @ 3771.084066
2025-07-21 14:01:38 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.03410036419188957 BNBUSDT @ 760.1590014
2025-07-21 14:01:44 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.03571428571428571 BNBUSDT @ 759.8549985999999
2025-07-21 14:01:48 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006789603758724641 ETHUSDT @ 3780.65598
2025-07-21 14:01:49 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006613931585491679 ETHUSDT @ 3780.65598
2025-07-21 14:01:50 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.03289443386705649 BNBUSDT @ 760.8478
2025-07-21 14:01:56 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.033809826487970464 BNBUSDT @ 729.334104
2025-07-21 14:01:58 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006613931585491679 ETHUSDT @ 3779.244
2025-07-21 14:02:02 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.0328515111695138 BNBUSDT @ 761.1522
2025-07-21 14:02:03 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.0067326828663993345 ETHUSDT @ 3780.756
2025-07-21 14:02:09 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.0066307193269554655 ETHUSDT @ 3779.244
2025-07-21 14:02:14 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006668551643931351 ETHUSDT @ 3748.190212
2025-07-21 14:02:19 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.00661377411051352 ETHUSDT @ 3780.745998
2025-07-21 14:02:20 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006613931585491679 ETHUSDT @ 3779.2340019999997
2025-07-21 14:02:28 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.033809826487970464 BNBUSDT @ 750.85014
2025-07-21 14:02:28 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.03330225123218329 BNBUSDT @ 750.5498600000001
2025-07-21 14:02:33 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.0328515111695138 BNBUSDT @ 760.8478
2025-07-21 14:02:34 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.0328515111695138 BNBUSDT @ 761.1522
2025-07-21 14:02:37 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006613931585491679 ETHUSDT @ 3780.65598
2025-07-21 14:02:45 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006668551643931351 ETHUSDT @ 3780.65598
2025-07-21 14:02:51 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.00661377411051352 ETHUSDT @ 3779.14402
2025-07-21 14:02:52 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 27.645692801061596 ADAUSDT @ 0.9044808599999999
2025-07-21 14:02:54 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006642629205780681 ETHUSDT @ 3762.8172860000004
2025-07-21 14:02:56 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.032175032175032175 BNBUSDT @ 761.1522
2025-07-21 14:02:59 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.03330225123218329 BNBUSDT @ 777.1554
2025-07-21 14:03:00 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006613756613756613 ETHUSDT @ 3780.756
2025-07-21 14:03:00 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 27.77777777777778 ADAUSDT @ 0.90411914
2025-07-21 14:03:02 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.032175032175032175 BNBUSDT @ 777.1554
2025-07-21 14:03:05 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.0328515111695138 BNBUSDT @ 776.8446
2025-07-21 14:03:08 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006613931585491679 ETHUSDT @ 3779.14402
2025-07-21 14:03:09 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.03571428571428571 BNBUSDT @ 699.86
2025-07-21 14:03:12 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 1.2413108242303872 LINKUSDT @ 20.135972000000002
2025-07-21 14:03:13 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006638008172715662 ETHUSDT @ 3779.14402
2025-07-21 14:03:15 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.032175032175032175 BNBUSDT @ 777.1554
2025-07-21 14:03:24 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 27.645692801061596 ADAUSDT @ 0.8998200000000001
2025-07-21 14:03:25 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006642629205780681 ETHUSDT @ 3780.745998
2025-07-21 14:03:28 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.032175032175032175 BNBUSDT @ 776.8446
2025-07-21 14:03:31 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006613756613756613 ETHUSDT @ 3779.2340019999997
2025-07-21 14:03:31 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 27.77777777777778 ADAUSDT @ 0.90018
2025-07-21 14:03:33 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.032175032175032175 BNBUSDT @ 776.8446
2025-07-21 14:03:40 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.03571428571428571 BNBUSDT @ 777.1554
2025-07-21 14:03:43 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 1.2413108242303872 LINKUSDT @ 20.1260244
2025-07-21 14:03:45 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006638008172715662 ETHUSDT @ 3780.745998
2025-07-21 14:03:46 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.032175032175032175 BNBUSDT @ 776.7446199999999
2025-07-21 14:04:04 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 25.77319587628866 ADAUSDT @ 0.90018
2025-07-21 14:04:10 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 25.77319587628866 ADAUSDT @ 0.969806
2025-07-21 14:04:11 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006646019698802387 ETHUSDT @ 3779.2340019999997
2025-07-21 14:04:16 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 25.77319587628866 ADAUSDT @ 0.970194
2025-07-21 14:04:22 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 27.691626052281787 ADAUSDT @ 0.90261944
2025-07-21 14:04:31 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 27.77777777777778 ADAUSDT @ 0.8998200000000001
2025-07-21 14:04:36 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 25.77319587628866 ADAUSDT @ 0.8998200000000001
2025-07-21 14:04:40 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 27.691626052281787 ADAUSDT @ 0.90298056
2025-07-21 14:04:41 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 25.77319587628866 ADAUSDT @ 0.90298056
2025-07-21 14:04:42 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.006646019698802387 ETHUSDT @ 3780.65598
2025-07-21 14:04:46 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006638360696656125 ETHUSDT @ 3765.236802
2025-07-21 14:04:48 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 25.77319587628866 ADAUSDT @ 0.8998200000000001
2025-07-21 14:04:54 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 27.691626052281787 ADAUSDT @ 0.9003800399999999
2025-07-21 14:04:59 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.006638360696656125 ETHUSDT @ 3770.385772
2025-07-21 14:05:03 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 27.77777777777778 ADAUSDT @ 0.90018
2025-07-21 14:05:12 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 27.691626052281787 ADAUSDT @ 0.8998200000000001
2025-07-21 14:06:25 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 14:06:25 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:09:04 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 14:09:04 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:09:09 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.02633304454761146 ETHUSDT @ 3793.7586
2025-07-21 14:09:09 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.02633304454761146 ETHUSDT @ 3792.2414
2025-07-21 14:09:10 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.02633304454761146 ETHUSDT @ 3798.269502
2025-07-21 14:09:11 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.02633304454761146 ETHUSDT @ 3792.2414
2025-07-21 14:09:12 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.02633276717883899 ETHUSDT @ 3798.30951
2025-07-21 14:09:12 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.5228211428870183 SOLUSDT @ 191.20823399999998
2025-07-21 14:09:13 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.5228211428870183 SOLUSDT @ 191.23174600000002
2025-07-21 14:09:14 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.5228211428870183 SOLUSDT @ 191.308254
2025-07-21 14:09:15 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 110.93854004881295 ADAUSDT @ 0.90158028
2025-07-21 14:09:16 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 110.93854004881295 ADAUSDT @ 0.8998200000000001
2025-07-21 14:09:17 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 110.93854004881295 ADAUSDT @ 0.9011802
2025-07-21 14:09:17 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 110.93854004881295 ADAUSDT @ 0.9008198000000001
2025-07-21 14:09:19 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.02633276717883899 ETHUSDT @ 3795.550738
2025-07-21 14:09:21 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.026329993812451456 ETHUSDT @ 3798.70959
2025-07-21 14:09:22 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.026329993812451456 ETHUSDT @ 3795.5407400000004
2025-07-21 14:09:25 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 110.93854004881295 ADAUSDT @ 0.90158028
2025-07-21 14:09:26 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.5228211428870183 SOLUSDT @ 191.241744
2025-07-21 14:09:26 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 110.93854004881295 ADAUSDT @ 0.8998200000000001
2025-07-21 14:09:27 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 110.93854004881295 ADAUSDT @ 0.90018
2025-07-21 14:09:28 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 110.93854004881295 ADAUSDT @ 0.8998200000000001
2025-07-21 14:09:29 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 110.97547442015315 ADAUSDT @ 0.90018
2025-07-21 14:09:30 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 110.97547442015315 ADAUSDT @ 0.9009197800000001
2025-07-21 14:09:31 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 110.97547442015315 ADAUSDT @ 0.90128022
2025-07-21 14:09:31 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 110.97547442015315 ADAUSDT @ 0.9009197800000001
2025-07-21 14:09:32 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 110.97547442015315 ADAUSDT @ 0.90128022
2025-07-21 14:09:34 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 110.97547442015315 ADAUSDT @ 0.8998200000000001
2025-07-21 14:09:41 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.026363104308258507 ETHUSDT @ 3792.421364
2025-07-21 14:11:05 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 14:11:05 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:14:25 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 14:14:25 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:14:45 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13200483665721513 ETHUSDT @ 3799.349718
2025-07-21 14:14:46 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13200483665721513 ETHUSDT @ 3798.130222
2025-07-21 14:14:48 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13200483665721513 ETHUSDT @ 3788.497548
2025-07-21 14:14:49 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13200483665721513 ETHUSDT @ 3797.130422
2025-07-21 14:14:50 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13161739350178606 ETHUSDT @ 3798.130222
2025-07-21 14:14:51 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13161739350178606 ETHUSDT @ 3798.6495779999996
2025-07-21 14:14:57 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13234690771450125 ETHUSDT @ 3778.7055899999996
2025-07-21 14:14:58 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13234690771450125 ETHUSDT @ 3798.09023
2025-07-21 14:14:59 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13161877936744015 ETHUSDT @ 3777.19441
2025-07-21 14:15:00 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13161877936744015 ETHUSDT @ 3799.60977
2025-07-21 14:15:01 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13234690771450125 ETHUSDT @ 3799.60977
2025-07-21 14:15:02 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13234690771450125 ETHUSDT @ 3798.09023
2025-07-21 14:15:03 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13161877936744015 ETHUSDT @ 3798.09023
2025-07-21 14:16:16 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13161877936744015 ETHUSDT @ 3785.757
2025-07-21 14:16:18 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13210039630118892 ETHUSDT @ 3799.35972
2025-07-21 14:16:29 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13210039630118892 ETHUSDT @ 3793.571134
2025-07-21 14:16:30 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13177557039055643 ETHUSDT @ 3793.571134
2025-07-21 14:16:32 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13177557039055643 ETHUSDT @ 3799.039656
2025-07-21 14:16:33 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.1316385311246143 ETHUSDT @ 3793.571134
2025-07-21 14:17:07 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.1316385311246143 ETHUSDT @ 3786.7572
2025-07-21 14:17:08 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13206550449022716 ETHUSDT @ 3786.7572
2025-07-21 14:17:17 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13206550449022716 ETHUSDT @ 3784.9828519999996
2025-07-21 14:17:18 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13207457458779526 ETHUSDT @ 3784.9828519999996
2025-07-21 14:17:19 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13207457458779526 ETHUSDT @ 3779.745798
2025-07-21 14:17:20 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.1323104850767004 ETHUSDT @ 3779.745798
2025-07-21 14:17:20 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.1323104850767004 ETHUSDT @ 3755.478754
2025-07-21 14:17:21 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.1323104850767004 ETHUSDT @ 3756.981246
2025-07-21 14:17:21 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.1323104850767004 ETHUSDT @ 3778.2342019999996
2025-07-21 14:17:28 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13337921578356288 ETHUSDT @ 3749.459742
2025-07-21 14:17:28 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: sell 0.13337921578356288 ETHUSDT @ 3747.860278
2025-07-21 14:17:29 | INFO     | arbtrader.core.logger:log_trade:80 | PAPER: Futures market order simulated: buy 0.13338277388151876 ETHUSDT @ 3749.359722
2025-07-21 14:44:28 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 14:44:28 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:45:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for BTCUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:45:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for BTCUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:46:52 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 14:46:52 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:47:11 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3796.99
2025-07-21 14:47:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:34 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:40 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:41 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:42 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:43 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:44 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:46 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:27 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 14:49:27 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:49:46 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3784.0
2025-07-21 14:49:47 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:49 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:51 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:52 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:53 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:54 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:55 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:56 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:57 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:57 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:58 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:59 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:00 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:01 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:02 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:03 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:04 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:05 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:06 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:07 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:07 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:08 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:09 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:10 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:11 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:11 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:14 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:18 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:22 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:23 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:24 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:24 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:25 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:26 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:27 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:28 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:28 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:29 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:30 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:30 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:32 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:33 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:34 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:35 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:36 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:41 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:42 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:42 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:43 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:44 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:44 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:45 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:46 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:47 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:47 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:49 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:40 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:41 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:47 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:49 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:51 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:52 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:53 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:54 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:54 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:55 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:56 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:56 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:58 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:00 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:01 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:03 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:04 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:04 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:05 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:06 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:07 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:08 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:09 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:11 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:14 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:17 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:19 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:57:01 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 14:57:01 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 14:57:17 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 ETHUSDT @ 3764.71
2025-07-21 14:57:19 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3769.8
2025-07-21 14:57:20 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3769.8
2025-07-21 14:57:21 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3665.1
2025-07-21 14:57:22 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3769.8
2025-07-21 14:57:23 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3769.8
2025-07-21 14:57:23 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3769.8
2025-07-21 14:57:24 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3769.8
2025-07-21 14:57:26 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3665.1
2025-07-21 14:57:27 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 ETHUSDT @ 3769.8
2025-07-21 14:57:28 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3769.8
2025-07-21 14:57:30 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 ETHUSDT @ 3665.1
2025-07-21 14:57:32 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 LINKUSDT @ 20.079
2025-07-21 14:57:33 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:33 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 LINKUSDT @ 20.079
2025-07-21 14:57:35 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:36 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:37 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3769.9
2025-07-21 14:57:38 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3769.9
2025-07-21 14:57:38 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:39 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:40 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:41 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:42 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:54 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:55 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 ETHUSDT @ 3673.57
2025-07-21 14:57:55 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:56 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: buy 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:57 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3766.84
2025-07-21 14:57:58 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3760.91
2025-07-21 14:57:59 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3764.71
2025-07-21 14:58:01 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3760.91
2025-07-21 14:58:02 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3760.91
2025-07-21 14:58:04 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3760.91
2025-07-21 14:58:04 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3760.91
2025-07-21 14:58:05 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3760.91
2025-07-21 14:58:07 | INFO     | arbtrader.core.logger:log_trade:80 | Futures market order executed: sell 0.0 ETHUSDT @ 3760.91
2025-07-21 14:58:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 14:58:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 14:58:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 14:58:14 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 14:58:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:00:19 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 15:00:19 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 15:00:32 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 15:00:32 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 15:01:12 | INFO     | arbtrader.core.futures_exchange:_load_futures_exchange_info:121 | Loaded info for 533 futures symbols
2025-07-21 15:01:12 | INFO     | arbtrader.core.futures_exchange:initialize:89 | Successfully connected to Binance Futures API
2025-07-21 15:01:36 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:40 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:41 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
