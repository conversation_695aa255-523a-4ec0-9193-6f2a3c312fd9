2025-07-19 00:35:59 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: 'NoneType' object has no attribute 'binance_api_key'
2025-07-19 00:37:28 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:42:41 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:44:22 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:44:41 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:50:30 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:57:25 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:57:25 | ERROR    | arbtrader.core.portfolio:initialize:73 | Failed to initialize exchange manager
2025-07-19 01:06:14 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize portfolio manager: 'permissions'
2025-07-19 01:06:46 | ERROR    | arbtrader.core.logger:log_error:86 | Error in ticker stream: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for XRPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for TRBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ZECUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for LTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BNBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for NEOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for AVAXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for THETAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ICXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ADAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOSTUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DEFIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for YFIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BANDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ZILUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for EGLDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for XLMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for COMPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:51 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize triangular arbitrage engine: 'permissions'
2025-07-19 01:07:27 | ERROR    | arbtrader.core.logger:log_error:86 | Error in ticker stream: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for RUNEUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ALGOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BANDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOSTUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for SUSHIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BNBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for TRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for NEOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BALUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for OMGUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ONTUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:30 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DOGEUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:30 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for MKRUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for VETUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for EGLDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for UNIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for NEOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for SNXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:37 | ERROR    | arbtrader.core.logger:log_error:86 | Error in ticker stream: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ZECUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for KNCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for XLMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for AVAXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for XTZUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for TRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ADAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for TRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BANDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for CRVUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for SXPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for QTUMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DOGEUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BALUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ZRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ADAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for MKRUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for UNIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BNBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for XLMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for KAVAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for COMPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:21:02 | ERROR    | arbtrader.strategies.pairs_trading:start_monitoring:188 | No trading pairs available
2025-07-19 01:21:02 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:12 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:22 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:32 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:52 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:22:11 | ERROR    | arbtrader.strategies.pairs_trading:start_monitoring:188 | No trading pairs available
