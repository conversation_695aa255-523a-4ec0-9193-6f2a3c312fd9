2025-07-19 00:35:59 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: 'NoneType' object has no attribute 'binance_api_key'
2025-07-19 00:37:28 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:42:41 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:44:22 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:44:41 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:50:30 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:57:25 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize exchange connection: APIError(code=-2015): Invalid API-key, IP, or permissions for action.
2025-07-19 00:57:25 | ERROR    | arbtrader.core.portfolio:initialize:73 | Failed to initialize exchange manager
2025-07-19 01:06:14 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize portfolio manager: 'permissions'
2025-07-19 01:06:46 | ERROR    | arbtrader.core.logger:log_error:86 | Error in ticker stream: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for XRPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for TRBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ZECUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for LTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BNBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:47 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for NEOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for AVAXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for THETAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ICXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ADAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOSTUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DEFIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:48 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for YFIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BANDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ZILUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for EGLDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for XLMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for COMPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:50 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:06:51 | ERROR    | arbtrader.core.logger:log_error:86 | Failed to initialize triangular arbitrage engine: 'permissions'
2025-07-19 01:07:27 | ERROR    | arbtrader.core.logger:log_error:86 | Error in ticker stream: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for RUNEUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ALGOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BANDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOSTUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for SUSHIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:28 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BNBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for TRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for NEOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BALUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for OMGUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:29 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ONTUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:30 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DOGEUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:30 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for MKRUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for VETUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for EGLDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for UNIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for NEOUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:07:31 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for SNXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:37 | ERROR    | arbtrader.core.logger:log_error:86 | Error in ticker stream: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ZECUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for KNCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for XLMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for AVAXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:39 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for XTZUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for TRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BTCUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ADAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for TRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BANDUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for CRVUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for ATOMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:40 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for SXPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for QTUMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DOGEUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BALUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ZRXUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ADAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BATUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for MKRUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for UNIUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:41 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for BNBUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for ETHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for DASHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for XLMUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for KAVAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for IOTAUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in kline stream for COMPUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:10:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error in orderbook stream for BCHUSDT: 'async for' requires an object with __aiter__ method, got ReconnectingWebsocket
2025-07-19 01:21:02 | ERROR    | arbtrader.strategies.pairs_trading:start_monitoring:188 | No trading pairs available
2025-07-19 01:21:02 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:12 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:22 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:32 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:42 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:21:52 | ERROR    | arbtrader.core.logger:log_error:86 | Error calculating basis opportunity for EOSUSDT/EOSUSDT: 'FuturesExchangeManager' object has no attribute 'get_ticker_price'
2025-07-19 01:22:11 | ERROR    | arbtrader.strategies.pairs_trading:start_monitoring:188 | No trading pairs available
2025-07-21 14:45:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for BTCUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:45:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for BTCUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:34 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:40 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:41 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:42 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:43 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:44 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:47:46 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:47 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:49 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:51 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:52 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:53 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:54 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:55 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:56 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:57 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:57 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:58 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:49:59 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:00 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:01 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:02 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:03 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:04 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:05 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:06 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:07 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:07 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:08 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:09 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:10 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:11 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:11 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:14 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:18 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:22 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:23 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:24 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:24 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:25 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:26 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:27 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:28 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:28 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:29 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:30 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:30 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:32 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:33 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:34 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:35 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:36 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:41 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:42 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:42 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:43 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:44 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:44 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:45 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:46 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:47 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:47 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:49 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:50:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:40 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:41 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:47 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:48 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:49 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:50 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:51 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:52 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:53 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:54 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:54 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:55 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:56 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:56 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:51:58 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:00 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:01 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:03 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:04 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:04 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:05 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:06 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:07 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:08 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:09 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:11 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:14 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:16 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:17 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:52:19 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-4003): Quantity less than or equal to zero.
2025-07-21 14:58:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 14:58:12 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 14:58:13 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 14:58:14 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 14:58:15 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:36 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:37 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:38 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:39 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:40 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
2025-07-21 15:01:41 | ERROR    | arbtrader.core.logger:log_error:86 | Unexpected error placing futures order for ETHUSDT: APIError(code=-2019): Margin is insufficient.
