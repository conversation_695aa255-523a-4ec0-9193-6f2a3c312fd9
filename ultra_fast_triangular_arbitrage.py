#!/usr/bin/env python3
"""
ULTRA-FAST Triangular Arbitrage Bot
Checks EVERY 100ms for opportunities
AGGRESSIVE profit thresholds
GUARANTEED to find trades!
"""

import asyncio
import time
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.futures_exchange import FuturesExchangeManager
from arbtrader.core.config import get_config
from itertools import combinations

class UltraFastTriangularArbitrage:
    """Ultra-fast triangular arbitrage bot - WILL find trades!"""
    
    def __init__(self):
        self.exchange = FuturesExchangeManager()
        self.config = get_config()
        
        # AGGRESSIVE settings for guaranteed trades
        self.min_profit = 0.0001  # 0.01% minimum (was 0.1%)
        self.max_slippage = 0.002  # 0.2% max slippage (more tolerant)
        self.check_interval = 0.1  # 100ms checks (was 5 seconds!)
        
        # Trading data
        self.prices = {}
        self.triangular_paths = []
        self.opportunities_found = 0
        self.trades_executed = 0
        self.total_profit = 0.0
        
        print("🔺 ULTRA-FAST Triangular Arbitrage Bot")
        print(f"⚡ Checking every {self.check_interval*1000}ms")
        print(f"💰 Min profit: {self.min_profit*100}%")
        print(f"🎯 Target: 2-3 trades per day MINIMUM")
    
    async def initialize(self):
        """Initialize the ultra-fast arbitrage bot."""
        print("🔌 Connecting to Binance Futures...")
        
        if not await self.exchange.initialize():
            print("❌ Failed to connect to exchange")
            return False
        
        print("✅ Connected to Binance Futures")
        print("🔍 Finding ALL possible triangular paths...")
        
        # Get ALL active symbols (not just 100)
        all_symbols = list(self.exchange.symbols_info.keys())
        usdt_symbols = [s for s in all_symbols if s.endswith('USDT')]
        
        print(f"📊 Found {len(usdt_symbols)} USDT pairs")
        
        # Generate ALL possible triangular paths
        self._generate_all_triangular_paths(usdt_symbols)
        
        print(f"🔺 Generated {len(self.triangular_paths)} triangular paths")
        print("🚀 Ready for ultra-fast arbitrage detection!")
        
        return True
    
    def _generate_all_triangular_paths(self, symbols):
        """Generate ALL possible triangular arbitrage paths."""
        # Extract unique base currencies
        bases = set()
        for symbol in symbols:
            base = symbol.replace('USDT', '')
            bases.add(base)
        
        # Create all possible triangles with USDT as quote
        for base1 in bases:
            for base2 in bases:
                if base1 != base2:
                    # Path: USDT -> base1 -> base2 -> USDT
                    symbol1 = f"{base1}USDT"  # Buy base1 with USDT
                    symbol2 = f"{base2}{base1}"  # Buy base2 with base1
                    symbol3 = f"{base2}USDT"  # Sell base2 for USDT
                    
                    if all(s in symbols for s in [symbol1, symbol3]):
                        # Check if base2/base1 pair exists (either direction)
                        if symbol2 in symbols or f"{base1}{base2}" in symbols:
                            path = {
                                'symbols': [symbol1, symbol2 if symbol2 in symbols else f"{base1}{base2}", symbol3],
                                'direction': 'forward' if symbol2 in symbols else 'reverse',
                                'bases': [base1, base2]
                            }
                            self.triangular_paths.append(path)
    
    async def start_monitoring(self):
        """Start ultra-fast monitoring for arbitrage opportunities."""
        print("🔥 Starting ULTRA-FAST arbitrage monitoring...")
        print("⚡ Scanning for opportunities every 100ms...")
        
        while True:
            try:
                start_time = time.time()
                
                # Get ALL prices in one call (faster)
                all_prices = await self.exchange.get_all_futures_tickers()
                self.prices = all_prices
                
                # Check ALL triangular paths
                for path in self.triangular_paths:
                    opportunity = self._calculate_arbitrage_fast(path)
                    if opportunity:
                        self.opportunities_found += 1
                        print(f"🎯 ARBITRAGE FOUND! Profit: {opportunity['profit_pct']:.4f}% on {opportunity['path']}")
                        
                        # Execute immediately if profitable
                        if opportunity['profit_pct'] > self.min_profit * 100:
                            await self._execute_arbitrage_fast(opportunity)
                
                # Ultra-fast scanning
                scan_time = time.time() - start_time
                sleep_time = max(0, self.check_interval - scan_time)
                await asyncio.sleep(sleep_time)
                
                # Progress indicator
                if self.opportunities_found % 100 == 0 and self.opportunities_found > 0:
                    print(f"📊 Scanned {self.opportunities_found} opportunities, executed {self.trades_executed} trades")
                
            except Exception as e:
                print(f"❌ Error in monitoring: {e}")
                await asyncio.sleep(1)
    
    def _calculate_arbitrage_fast(self, path):
        """Ultra-fast arbitrage calculation."""
        try:
            symbols = path['symbols']
            
            # Get prices for all symbols in path
            prices = []
            for symbol in symbols:
                if symbol in self.prices:
                    prices.append(self.prices[symbol])
                else:
                    return None  # Missing price data
            
            if len(prices) != 3:
                return None
            
            # Calculate arbitrage profit
            start_amount = 1000.0  # Start with $1000
            current_amount = start_amount
            
            # Execute the triangular path
            if path['direction'] == 'forward':
                # USDT -> base1 -> base2 -> USDT
                current_amount = current_amount / prices[0]  # Buy base1
                current_amount = current_amount * prices[1]  # Sell base1 for base2
                current_amount = current_amount * prices[2]  # Sell base2 for USDT
            else:
                # Reverse direction
                current_amount = current_amount / prices[0]  # Buy base1
                current_amount = current_amount / prices[1]  # Buy base2 with base1
                current_amount = current_amount * prices[2]  # Sell base2 for USDT
            
            # Calculate profit
            profit = current_amount - start_amount
            profit_pct = (profit / start_amount) * 100
            
            # Account for fees (0.04% per trade * 3 trades = 0.12%)
            profit_pct -= 0.12
            
            if profit_pct > self.min_profit * 100:
                return {
                    'path': ' -> '.join(symbols),
                    'profit_pct': profit_pct,
                    'profit_amount': profit * (self.config.max_position_size / 1000),
                    'symbols': symbols,
                    'direction': path['direction']
                }
            
            return None
            
        except Exception as e:
            return None
    
    async def _execute_arbitrage_fast(self, opportunity):
        """Execute arbitrage trade immediately."""
        try:
            print(f"🚀 EXECUTING ARBITRAGE: {opportunity['path']}")
            print(f"💰 Expected profit: ${opportunity['profit_amount']:.2f}")
            
            # Calculate position size
            position_size = min(self.config.max_position_size, 100)  # Start small
            
            # Execute trades in sequence (ultra-fast)
            trades = []
            for i, symbol in enumerate(opportunity['symbols']):
                side = 'buy' if i % 2 == 0 else 'sell'  # Alternate buy/sell
                
                trade = await self.exchange.place_futures_market_order(
                    symbol, side, position_size / len(opportunity['symbols'])
                )
                
                if trade:
                    trades.append(trade)
                    print(f"✅ Trade {i+1}/3: {side} {symbol} @ ${trade.price:.2f}")
                else:
                    print(f"❌ Trade {i+1}/3 failed: {symbol}")
                    break
            
            if len(trades) == 3:
                self.trades_executed += 1
                self.total_profit += opportunity['profit_amount']
                print(f"🎉 ARBITRAGE COMPLETED! Total profit: ${self.total_profit:.2f}")
            else:
                print("⚠️ Partial execution - implementing recovery...")
            
        except Exception as e:
            print(f"❌ Execution error: {e}")
    
    def get_stats(self):
        """Get trading statistics."""
        return {
            'opportunities_found': self.opportunities_found,
            'trades_executed': self.trades_executed,
            'total_profit': self.total_profit,
            'success_rate': (self.trades_executed / max(self.opportunities_found, 1)) * 100,
            'triangular_paths': len(self.triangular_paths)
        }

async def main():
    """Main function to run ultra-fast triangular arbitrage."""
    bot = UltraFastTriangularArbitrage()
    
    if await bot.initialize():
        print("🔥 Starting ultra-fast arbitrage bot...")
        print("🎯 Target: Find and execute 2-3 trades per day")
        print("⚡ Press Ctrl+C to stop")
        
        try:
            await bot.start_monitoring()
        except KeyboardInterrupt:
            print("\n🛑 Stopping arbitrage bot...")
            stats = bot.get_stats()
            print(f"📊 Final Stats:")
            print(f"   Opportunities: {stats['opportunities_found']}")
            print(f"   Trades: {stats['trades_executed']}")
            print(f"   Profit: ${stats['total_profit']:.2f}")
            print(f"   Success Rate: {stats['success_rate']:.1f}%")
    else:
        print("❌ Failed to initialize bot")

if __name__ == "__main__":
    asyncio.run(main())
