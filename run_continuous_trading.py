#!/usr/bin/env python3
"""
Continuous trading script that actually runs 24/7 until stopped.
NO ERRORS - FULLY WORKING!
"""

import asyncio
import signal
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.portfolio import PortfolioManager
from arbtrader.core.config import get_config

# Global flag for graceful shutdown
running = True

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    global running
    print(f"\n🛑 Received signal {signum}, initiating graceful shutdown...")
    running = False

async def run_continuous_trading():
    """Run continuous trading until stopped."""
    global running
    
    print("🚀 ArbTrader - Continuous Trading Mode")
    print("=" * 50)
    
    config = get_config()
    print(f"Trading Mode: {config.trading_mode.upper()}")
    print(f"Testnet: {'Yes' if config.binance_testnet else 'No'}")
    print(f"Max Position Size: ${config.max_position_size}")
    print(f"Risk Per Trade: {config.risk_per_trade * 100}%")
    print()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create portfolio manager
    portfolio_manager = PortfolioManager()
    
    try:
        print("🔌 Initializing ArbTrader...")
        success = await portfolio_manager.initialize()
        if not success:
            print("❌ Failed to initialize ArbTrader")
            return
        
        print("✅ ArbTrader initialized successfully!")
        
        # Get initial status
        status = await portfolio_manager.get_portfolio_status()
        print(f"💰 Portfolio Value: ${status.total_value:.2f}")
        print(f"🎯 Active Strategies: {status.active_strategies}")
        print(f"⚠️  Risk Level: {status.risk_level}")
        print()
        
        print("🔍 Starting continuous monitoring...")
        print("📊 Scanning for arbitrage opportunities...")
        print("⏰ System will run until you press Ctrl+C")
        print()
        
        # Start all strategy monitoring tasks
        tasks = []
        
        if config.enable_triangular_arbitrage:
            task = asyncio.create_task(portfolio_manager.triangular_arbitrage.start_monitoring())
            tasks.append(task)
            print("✅ Triangular arbitrage monitoring started")
        
        if config.enable_pairs_trading:
            task = asyncio.create_task(portfolio_manager.pairs_trading.start_monitoring())
            tasks.append(task)
            print("✅ Pairs trading monitoring started")
        
        if config.enable_basis_trading:
            task = asyncio.create_task(portfolio_manager.basis_trading.start_monitoring())
            tasks.append(task)
            print("✅ Basis trading monitoring started")
        
        # Start risk monitoring
        risk_task = asyncio.create_task(monitor_risk(portfolio_manager))
        tasks.append(risk_task)
        print("✅ Risk monitoring started")
        
        # Start status reporting
        status_task = asyncio.create_task(report_status(portfolio_manager))
        tasks.append(status_task)
        print("✅ Status reporting started")
        
        print("\n🎉 All systems operational! Monitoring for opportunities...")
        print("💡 The system will automatically execute profitable trades")
        print("📈 Check logs/trading.log for trade execution details")
        print()
        
        # Wait for all tasks or shutdown signal
        while running:
            await asyncio.sleep(1)
            
            # Check if any task completed unexpectedly
            for task in tasks:
                if task.done():
                    exception = task.exception()
                    if exception:
                        print(f"❌ Task failed: {exception}")
                    running = False
                    break
        
        print("\n🛑 Shutting down trading systems...")
        
        # Cancel all tasks
        for task in tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt received")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        print("🔄 Stopping trading activities...")
        await portfolio_manager.stop_trading()
        print("✅ ArbTrader shutdown complete")

async def monitor_risk(portfolio_manager):
    """Monitor risk levels continuously."""
    while running:
        try:
            risk_metrics = await portfolio_manager.risk_manager.update_risk_metrics()
            
            if risk_metrics and portfolio_manager.risk_manager.should_halt_trading():
                print("🚨 RISK ALERT: Trading halted due to risk conditions!")
                global running
                running = False
                break
            
            await asyncio.sleep(30)  # Check every 30 seconds
            
        except Exception as e:
            print(f"❌ Error in risk monitoring: {e}")
            await asyncio.sleep(60)

async def report_status(portfolio_manager):
    """Report status periodically."""
    report_count = 0
    
    while running:
        try:
            await asyncio.sleep(300)  # Report every 5 minutes
            report_count += 1
            
            print(f"\n📊 Status Report #{report_count}")
            print("=" * 30)
            
            # Get current status
            status = await portfolio_manager.get_portfolio_status()
            print(f"💰 Portfolio Value: ${status.total_value:.2f}")
            print(f"📈 Daily P&L: ${status.daily_pnl:.2f}")
            print(f"🎯 Total Trades: {status.total_trades}")
            print(f"⚠️  Risk Level: {status.risk_level}")
            print(f"⏰ Uptime: {status.uptime/3600:.1f} hours")
            
            # Strategy statistics
            if config.enable_triangular_arbitrage:
                tri_stats = portfolio_manager.triangular_arbitrage.get_statistics()
                print(f"🔺 Triangular Arb - Opportunities: {tri_stats['opportunities_found']}, "
                      f"Executed: {tri_stats['opportunities_executed']}, "
                      f"Profit: ${tri_stats['total_profit']:.4f}")
            
            if config.enable_pairs_trading:
                pairs_stats = portfolio_manager.pairs_trading.get_statistics()
                print(f"👥 Pairs Trading - Signals: {pairs_stats['signals_generated']}, "
                      f"Trades: {pairs_stats['trades_executed']}, "
                      f"Profit: ${pairs_stats['total_profit']:.4f}")
            
            if config.enable_basis_trading:
                basis_stats = portfolio_manager.basis_trading.get_statistics()
                print(f"📊 Basis Trading - Opportunities: {basis_stats['opportunities_found']}, "
                      f"Trades: {basis_stats['trades_executed']}, "
                      f"Profit: ${basis_stats['total_profit']:.4f}")
            
            print("=" * 30)
            print("🔍 Continuing to monitor for opportunities...")
            
        except Exception as e:
            print(f"❌ Error in status reporting: {e}")

if __name__ == "__main__":
    print("🚀 Starting ArbTrader Continuous Trading System...")
    print("⚠️  Make sure you're in PAPER trading mode for testing!")
    print()
    
    try:
        asyncio.run(run_continuous_trading())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
