#!/usr/bin/env python3
"""
Simple API test script to verify Binance API connectivity.
This script tests both Spot and Futures testnet connections.
"""

import os
import asyncio
from binance.client import Client
from binance import AsyncClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

API_KEY = os.getenv('BINANCE_API_KEY')
SECRET_KEY = os.getenv('BINANCE_SECRET_KEY')

print("🔍 Binance API Connection Test")
print("=" * 50)

if not API_KEY or not SECRET_KEY:
    print("❌ ERROR: API keys not found in .env file")
    print("Please check your .env file contains:")
    print("BINANCE_API_KEY=your_key_here")
    print("BINANCE_SECRET_KEY=your_secret_here")
    exit(1)

print(f"✓ API Key found: {API_KEY[:8]}...{API_KEY[-8:]}")
print(f"✓ Secret Key found: {SECRET_KEY[:8]}...{SECRET_KEY[-8:]}")
print()

async def test_spot_testnet():
    """Test Binance Spot Testnet API"""
    print("🧪 Testing Binance Spot Testnet...")
    try:
        # Test with sync client first
        client = Client(API_KEY, SECRET_KEY, testnet=True)
        
        # Test server time (no auth required)
        server_time = client.get_server_time()
        print(f"✓ Server time: {server_time}")
        
        # Test account info (requires valid API key)
        account = client.get_account()
        print(f"✓ Account type: {account.get('accountType', 'Unknown')}")
        print(f"✓ Can trade: {account.get('canTrade', False)}")
        print(f"✓ Balances: {len(account.get('balances', []))} assets")
        
        # Show some balances
        balances = [b for b in account.get('balances', []) if float(b['free']) > 0 or float(b['locked']) > 0]
        if balances:
            print("✓ Non-zero balances:")
            for balance in balances[:5]:  # Show first 5
                print(f"   {balance['asset']}: {balance['free']} (free), {balance['locked']} (locked)")
        else:
            print("ℹ️  No balances found (this is normal for new testnet accounts)")
        
        return True
        
    except Exception as e:
        print(f"❌ Spot Testnet Error: {e}")
        return False

async def test_futures_testnet():
    """Test Binance Futures Testnet API"""
    print("\n🧪 Testing Binance Futures Testnet...")
    try:
        # For futures, we need to use a different approach
        from binance.client import Client
        
        # Create futures client
        client = Client(API_KEY, SECRET_KEY, testnet=True)
        
        # Test futures account (this might fail if keys are not for futures)
        try:
            futures_account = client.futures_account()
            print(f"✓ Futures account found")
            print(f"✓ Total wallet balance: {futures_account.get('totalWalletBalance', 'Unknown')}")
            return True
        except Exception as futures_error:
            print(f"❌ Futures API Error: {futures_error}")
            return False
            
    except Exception as e:
        print(f"❌ Futures Testnet Error: {e}")
        return False

async def test_market_data():
    """Test public market data (no auth required)"""
    print("\n📊 Testing Public Market Data...")
    try:
        client = Client()  # No API key needed for public data
        
        # Test ticker price
        btc_price = client.get_symbol_ticker(symbol="BTCUSDT")
        print(f"✓ BTC/USDT Price: ${float(btc_price['price']):,.2f}")
        
        # Test order book
        order_book = client.get_order_book(symbol="BTCUSDT", limit=5)
        print(f"✓ Order book: {len(order_book['bids'])} bids, {len(order_book['asks'])} asks")
        
        return True
        
    except Exception as e:
        print(f"❌ Market Data Error: {e}")
        return False

async def test_async_client():
    """Test async client"""
    print("\n⚡ Testing Async Client...")
    try:
        async_client = await AsyncClient.create(API_KEY, SECRET_KEY, testnet=True)
        
        # Test account info
        account = await async_client.get_account()
        print(f"✓ Async client works")
        print(f"✓ Account status: {account.get('accountType', 'Unknown')}")
        
        await async_client.close_connection()
        return True
        
    except Exception as e:
        print(f"❌ Async Client Error: {e}")
        return False

def check_api_key_format():
    """Check if API keys have the right format"""
    print("\n🔍 Checking API Key Format...")
    
    # Binance API keys are typically 64 characters long
    if len(API_KEY) != 64:
        print(f"⚠️  Warning: API key length is {len(API_KEY)}, expected 64 characters")
    else:
        print("✓ API key length looks correct (64 characters)")
    
    if len(SECRET_KEY) != 64:
        print(f"⚠️  Warning: Secret key length is {len(SECRET_KEY)}, expected 64 characters")
    else:
        print("✓ Secret key length looks correct (64 characters)")
    
    # Check if keys are hexadecimal
    try:
        int(API_KEY, 16)
        print("✓ API key format looks correct (hexadecimal)")
    except ValueError:
        print("⚠️  Warning: API key doesn't appear to be hexadecimal")
    
    try:
        int(SECRET_KEY, 16)
        print("✓ Secret key format looks correct (hexadecimal)")
    except ValueError:
        print("⚠️  Warning: Secret key doesn't appear to be hexadecimal")

async def main():
    """Main test function"""
    check_api_key_format()
    
    # Test public data first (no auth needed)
    market_data_ok = await test_market_data()
    
    # Test authenticated endpoints
    spot_ok = await test_spot_testnet()
    futures_ok = await test_futures_testnet()
    async_ok = await test_async_client()
    
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    print(f"Market Data: {'✓ PASS' if market_data_ok else '❌ FAIL'}")
    print(f"Spot Testnet: {'✓ PASS' if spot_ok else '❌ FAIL'}")
    print(f"Futures Testnet: {'✓ PASS' if futures_ok else '❌ FAIL'}")
    print(f"Async Client: {'✓ PASS' if async_ok else '❌ FAIL'}")
    
    if not spot_ok:
        print("\n💡 Troubleshooting Tips:")
        print("1. Make sure you're using SPOT testnet API keys from: https://testnet.binance.vision/")
        print("2. NOT futures testnet keys from: https://testnet.binancefuture.com/")
        print("3. Check that your API key has 'Enable Spot & Margin Trading' permission")
        print("4. Verify your IP is whitelisted (or disable IP restriction)")
        print("5. Make sure the keys are correctly copied (no extra spaces)")

if __name__ == "__main__":
    asyncio.run(main())
