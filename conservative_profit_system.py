#!/usr/bin/env python3
"""
CONSERVATIVE PROFIT SYSTEM
🎯 SMALL, CONSISTENT PROFITS - NO BIG LOSSES
💰 GOAL: RECOVER TO $14,800+ WITH CAREFUL TRADING
✅ SMALLER POSITIONS ✅ BETTER RISK MANAGEMENT ✅ DIVERSIFIED
"""

import asyncio
import time
import sys
import os
import numpy as np
from collections import deque
from binance.client import Client
from binance.enums import *
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.config import get_config

class ConservativeProfitSystem:
    """Conservative profit system - small, consistent gains!"""
    
    def __init__(self):
        self.config = get_config()
        
        # Direct Binance client
        self.client = Client(
            api_key=self.config.binance_api_key,
            api_secret=self.config.binance_secret_key,
            testnet=True
        )
        
        # CONSERVATIVE settings
        self.current_balance = 14468.0  # Current balance after losses
        self.starting_balance = 14468.0
        self.target_balance = 14800.0  # Target
        self.target_profit = self.target_balance - self.current_balance  # $332 needed
        
        # CONSERVATIVE trading parameters
        self.profit_target = 0.012  # 1.2% profit target (higher for better R:R)
        self.stop_loss = 0.006  # 0.6% stop loss (2:1 risk/reward)
        self.check_interval = 3.0  # 3 second checks (less aggressive)
        self.deviation_threshold = 0.008  # 0.8% deviation (stronger signals)
        
        # SMALLER position sizing for safety
        self.min_trade_value = 30.0  # Minimum $30 per trade
        self.max_position_pct = 0.02  # Maximum 2% of balance per trade (MUCH smaller)
        self.daily_loss_limit = 50.0  # Max $50 loss per day
        
        # Risk management
        self.total_profit = 0.0
        self.daily_loss = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.consecutive_losses = 0
        self.max_consecutive_losses = 3  # Stop after 3 losses in a row
        
        # Symbol cooldowns to prevent repeated mistakes
        self.symbol_cooldowns = {}
        self.cooldown_period = 300  # 5 minute cooldown after loss
        
        # DIVERSIFIED but SAFE symbols
        self.target_symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'
        ]
        
        # Price tracking
        self.price_history = {}
        self.moving_averages = {}
        self.active_positions = {}
        
        print("🛡️ CONSERVATIVE PROFIT SYSTEM")
        print(f"💰 Current: ${self.current_balance:,.2f}")
        print(f"🎯 Target: ${self.target_balance:,.2f}")
        print(f"📈 Need: ${self.target_profit:,.2f} profit")
        print(f"🔒 Max position: {self.max_position_pct*100}% of balance")
        print(f"🛡️ Daily loss limit: ${self.daily_loss_limit}")
    
    async def initialize(self):
        """Initialize conservative system."""
        print("🔌 Connecting for CONSERVATIVE profit making...")
        
        try:
            # Get real balance
            account_info = self.client.futures_account()
            for balance in account_info['assets']:
                if balance['asset'] == 'USDT':
                    real_balance = float(balance['walletBalance'])
                    print(f"💰 Real balance: ${real_balance:,.2f} USDT")
                    self.current_balance = real_balance
                    self.starting_balance = real_balance
                    self.target_profit = self.target_balance - real_balance
                    break
            
            print("✅ Connected for CONSERVATIVE trading")
            
            # Set 1x leverage
            for symbol in ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']:
                try:
                    self.client.futures_change_leverage(symbol=symbol, leverage=1)
                except:
                    pass
            
            # Initialize tracking
            for symbol in self.target_symbols:
                self.price_history[symbol] = deque(maxlen=50)
                self.moving_averages[symbol] = {'sma_20': 0, 'sma_50': 0}
            
            # Collect market data (longer period for better signals)
            print("📈 Analyzing market for CONSERVATIVE opportunities...")
            for _ in range(30):
                tickers = self.client.futures_symbol_ticker()
                ticker_dict = {t['symbol']: float(t['price']) for t in tickers}
                
                for symbol in self.target_symbols:
                    if symbol in ticker_dict:
                        self.price_history[symbol].append(ticker_dict[symbol])
                await asyncio.sleep(2)
            
            # Calculate moving averages
            for symbol in self.target_symbols:
                await self._update_moving_averages(symbol)
            
            print("🚀 Ready for CONSERVATIVE profit making!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize: {e}")
            return False
    
    async def _update_moving_averages(self, symbol):
        """Update moving averages for conservative signals."""
        if len(self.price_history[symbol]) < 50:
            return
        
        prices = list(self.price_history[symbol])
        sma_20 = np.mean(prices[-20:])
        sma_50 = np.mean(prices[-50:])
        
        self.moving_averages[symbol] = {'sma_20': sma_20, 'sma_50': sma_50}
    
    async def start_conservative_trading(self):
        """Start conservative profit trading."""
        print("🛡️ Starting CONSERVATIVE profit trading...")
        print(f"🎯 Target: ${self.target_profit:,.2f} profit")
        print("📊 Small positions, consistent gains!")
        
        check_count = 0
        
        while self.total_profit < self.target_profit and self.daily_loss < self.daily_loss_limit:
            try:
                start_time = time.time()
                check_count += 1
                
                # Display status every 20 checks (1 minute)
                if check_count % 20 == 0:
                    await self._display_conservative_status()
                
                # Stop if too many consecutive losses
                if self.consecutive_losses >= self.max_consecutive_losses:
                    print(f"\n🛑 Stopping due to {self.consecutive_losses} consecutive losses")
                    break
                
                # Get current prices
                tickers = self.client.futures_symbol_ticker()
                current_prices = {t['symbol']: float(t['price']) for t in tickers}
                
                # Check for CONSERVATIVE opportunities
                for symbol in self.target_symbols:
                    if symbol in current_prices:
                        current_price = current_prices[symbol]
                        await self._check_conservative_opportunity(symbol, current_price)
                
                # Manage positions conservatively
                await self._manage_conservative_positions(current_prices)
                
                # Check if target reached
                if self.total_profit >= self.target_profit:
                    print(f"\n🎉 CONSERVATIVE TARGET REACHED!")
                    print(f"💰 Made ${self.total_profit:+.2f} profit!")
                    break
                
                # Update cooldowns
                current_time = time.time()
                expired_cooldowns = [s for s, t in self.symbol_cooldowns.items() if current_time - t > self.cooldown_period]
                for symbol in expired_cooldowns:
                    del self.symbol_cooldowns[symbol]
                
                scan_time = time.time() - start_time
                sleep_time = max(0, self.check_interval - scan_time)
                await asyncio.sleep(sleep_time)
                
            except Exception as e:
                print(f"❌ Error in conservative trading: {e}")
                await asyncio.sleep(5)
    
    async def _display_conservative_status(self):
        """Display conservative status."""
        profit_pct = (self.total_profit / self.starting_balance) * 100
        win_rate = (self.winning_trades / max(self.total_trades, 1)) * 100
        remaining_profit = self.target_profit - self.total_profit
        
        print(f"\n🛡️ CONSERVATIVE STATUS:")
        print(f"🎯 Target: ${self.target_profit:,.2f} | Made: ${self.total_profit:+.2f} | Need: ${remaining_profit:+.2f}")
        print(f"📈 Profit %: {profit_pct:+.2f}%")
        print(f"🏆 Win Rate: {win_rate:.1f}% ({self.winning_trades}/{self.total_trades})")
        print(f"🔴 Consecutive Losses: {self.consecutive_losses}")
        print(f"📊 Active Positions: {len(self.active_positions)}")
        print(f"❄️ Cooled Down: {len(self.symbol_cooldowns)} symbols")
    
    async def _check_conservative_opportunity(self, symbol, current_price):
        """Check for CONSERVATIVE opportunities only."""
        # Skip if symbol is in cooldown
        if symbol in self.symbol_cooldowns:
            return
        
        # Update price history
        self.price_history[symbol].append(current_price)
        await self._update_moving_averages(symbol)
        
        if len(self.price_history[symbol]) < 50:
            return
        
        # Skip if we already have a position
        existing_positions = [k for k in self.active_positions.keys() if symbol in k]
        if existing_positions:
            return
        
        # Get moving averages
        sma_20 = self.moving_averages[symbol]['sma_20']
        sma_50 = self.moving_averages[symbol]['sma_50']
        
        if sma_20 == 0 or sma_50 == 0:
            return
        
        # Calculate CONSERVATIVE signals
        deviation_20 = (current_price - sma_20) / sma_20
        deviation_50 = (current_price - sma_50) / sma_50
        trend_strength = (sma_20 - sma_50) / sma_50
        
        # Only trade on VERY STRONG signals
        if abs(deviation_20) >= self.deviation_threshold and abs(trend_strength) >= 0.005:
            
            # Calculate SMALL position size
            base_position = self.current_balance * self.max_position_pct
            position_value = max(self.min_trade_value, base_position)
            position_value = min(position_value, self.current_balance * 0.05)  # Max 5%
            
            # Only trade if we have enough balance and haven't hit loss limit
            if (position_value >= self.min_trade_value and 
                self.current_balance >= 500 and 
                self.daily_loss < self.daily_loss_limit):
                
                # Conservative buy signal
                if deviation_20 < -self.deviation_threshold and trend_strength > 0.005:
                    await self._execute_conservative_trade(symbol, 'BUY', current_price, position_value, deviation_20)
                
                # Conservative sell signal
                elif deviation_20 > self.deviation_threshold and trend_strength < -0.005:
                    await self._execute_conservative_trade(symbol, 'SELL', current_price, position_value, deviation_20)
    
    async def _execute_conservative_trade(self, symbol, side, entry_price, position_value, signal_strength):
        """Execute a CONSERVATIVE trade."""
        print(f"\n🛡️ CONSERVATIVE OPPORTUNITY: {symbol} {side}")
        print(f"📊 Signal: {signal_strength*100:.2f}%")
        print(f"💵 Size: ${position_value:.0f} ({(position_value/self.current_balance)*100:.1f}% of balance)")
        
        # Calculate proper quantity
        base_quantity = position_value / entry_price
        
        # Get symbol precision
        exchange_info = self.client.futures_exchange_info()
        symbol_info = next((s for s in exchange_info['symbols'] if s['symbol'] == symbol), None)
        
        if not symbol_info:
            return
        
        # Get filters
        lot_filter = next((f for f in symbol_info['filters'] if f['filterType'] == 'LOT_SIZE'), None)
        if not lot_filter:
            return
        
        step_size = float(lot_filter['stepSize'])
        min_qty = float(lot_filter['minQty'])
        
        # Round quantity properly
        if step_size >= 1:
            quantity = max(min_qty, round(base_quantity))
        elif step_size >= 0.1:
            quantity = max(min_qty, round(base_quantity, 1))
        elif step_size >= 0.01:
            quantity = max(min_qty, round(base_quantity, 2))
        else:
            quantity = max(min_qty, round(base_quantity, 3))
        
        # Verify minimum notional
        notional = quantity * entry_price
        if notional < self.min_trade_value:
            print(f"⚠️ Position too small: ${notional:.2f}")
            return
        
        print(f"📊 Quantity: {quantity} | Notional: ${notional:.2f}")
        
        try:
            # Execute conservative trade
            order = self.client.futures_create_order(
                symbol=symbol,
                side=side,
                type=ORDER_TYPE_MARKET,
                quantity=quantity
            )
            
            if order and 'orderId' in order:
                # Get order details
                order_info = self.client.futures_get_order(symbol=symbol, orderId=order['orderId'])
                
                if order_info and float(order_info['executedQty']) > 0:
                    fill_price = float(order_info['avgPrice'])
                    fill_qty = float(order_info['executedQty'])
                    
                    self.total_trades += 1
                    
                    # Calculate CONSERVATIVE targets
                    if side == 'BUY':
                        profit_target = fill_price * (1 + self.profit_target)
                        stop_loss = fill_price * (1 - self.stop_loss)
                    else:
                        profit_target = fill_price * (1 - self.profit_target)
                        stop_loss = fill_price * (1 + self.stop_loss)
                    
                    # Store position
                    position_key = f"{symbol}_{int(time.time()*1000)}"
                    self.active_positions[position_key] = {
                        'symbol': symbol,
                        'side': side.lower(),
                        'entry_price': fill_price,
                        'quantity': fill_qty,
                        'profit_target': profit_target,
                        'stop_loss': stop_loss,
                        'entry_time': time.time(),
                        'order_id': order['orderId'],
                        'position_value': fill_price * fill_qty
                    }
                    
                    print(f"✅ CONSERVATIVE TRADE EXECUTED!")
                    print(f"   ID: {order['orderId']}")
                    print(f"   {side} {fill_qty} {symbol} @ ${fill_price:.4f}")
                    print(f"   🎯 Target: ${profit_target:.4f} (+{self.profit_target*100:.1f}%)")
                    print(f"   🛡️ Stop: ${stop_loss:.4f} (-{self.stop_loss*100:.1f}%)")
                    
        except Exception as e:
            print(f"❌ Conservative trade failed: {e}")
    
    async def _manage_conservative_positions(self, current_prices):
        """Manage positions conservatively."""
        positions_to_close = []
        
        for position_key, position in self.active_positions.items():
            symbol = position['symbol']
            if symbol not in current_prices:
                continue
            
            current_price = current_prices[symbol]
            entry_price = position['entry_price']
            side = position['side']
            
            # Calculate P&L
            if side == 'buy':
                pnl = (current_price - entry_price) * position['quantity']
                pnl_pct = (current_price - entry_price) / entry_price
            else:
                pnl = (entry_price - current_price) * position['quantity']
                pnl_pct = (entry_price - current_price) / entry_price
            
            # Check exit conditions
            profit_hit = False
            stop_hit = False
            
            if side == 'buy':
                profit_hit = current_price >= position['profit_target']
                stop_hit = current_price <= position['stop_loss']
            else:
                profit_hit = current_price <= position['profit_target']
                stop_hit = current_price >= position['stop_loss']
            
            # Time-based exit (30 minutes max)
            time_exit = time.time() - position['entry_time'] > 1800
            
            if profit_hit or stop_hit or time_exit:
                exit_reason = "PROFIT" if profit_hit else "STOP" if stop_hit else "TIME"
                positions_to_close.append((position_key, position, current_price, pnl, pnl_pct, exit_reason))
        
        # Close positions
        for position_key, position, exit_price, pnl, pnl_pct, exit_reason in positions_to_close:
            await self._close_conservative_position(position_key, position, exit_price, pnl, pnl_pct, exit_reason)
    
    async def _close_conservative_position(self, position_key, position, exit_price, pnl, pnl_pct, exit_reason):
        """Close position conservatively."""
        symbol = position['symbol']
        side = position['side']
        close_side = 'SELL' if side == 'buy' else 'BUY'
        
        print(f"\n🛡️ CLOSING CONSERVATIVE POSITION: {symbol} ({exit_reason})")
        
        try:
            close_order = self.client.futures_create_order(
                symbol=symbol,
                side=close_side,
                type=ORDER_TYPE_MARKET,
                quantity=position['quantity']
            )
            
            if close_order and 'orderId' in close_order:
                # Calculate net profit (after fees)
                fees = (position['position_value'] + exit_price * position['quantity']) * 0.0004
                net_profit = pnl - fees
                
                self.total_profit += net_profit
                
                if net_profit > 0:
                    self.winning_trades += 1
                    self.consecutive_losses = 0  # Reset consecutive losses
                    emoji = "💰"
                else:
                    self.losing_trades += 1
                    self.consecutive_losses += 1
                    self.daily_loss += abs(net_profit)
                    # Add symbol to cooldown
                    self.symbol_cooldowns[symbol] = time.time()
                    emoji = "💔"
                
                hold_time = time.time() - position['entry_time']
                
                print(f"✅ CONSERVATIVE POSITION CLOSED!")
                print(f"   {emoji} Net Profit: ${net_profit:+.2f} ({pnl_pct*100:+.2f}%)")
                print(f"   ⏱️ Hold: {hold_time/60:.1f}min")
                print(f"   📊 Total Profit: ${self.total_profit:+.2f}")
                print(f"   🔴 Consecutive Losses: {self.consecutive_losses}")
                
                if net_profit < 0:
                    print(f"   ❄️ {symbol} cooled down for {self.cooldown_period/60:.0f} minutes")
                
                del self.active_positions[position_key]
                
        except Exception as e:
            print(f"❌ Failed to close conservative position: {e}")

async def main():
    """Main conservative trading function."""
    print("🛡️ CONSERVATIVE PROFIT SYSTEM")
    print("🎯 GOAL: RECOVER TO $14,800+ SAFELY")
    print("📈 SMALL POSITIONS, CONSISTENT GAINS")
    print()
    
    system = ConservativeProfitSystem()
    
    if await system.initialize():
        try:
            await system.start_conservative_trading()
            
            # Final report
            final_profit_pct = (system.total_profit / system.starting_balance) * 100
            win_rate = (system.winning_trades / max(system.total_trades, 1)) * 100
            
            print(f"\n🛡️ CONSERVATIVE TRADING COMPLETE!")
            print(f"💰 Starting: ${system.starting_balance:,.2f}")
            print(f"💰 Current: ${system.current_balance:,.2f}")
            print(f"📈 Total Profit: ${system.total_profit:+.2f} ({final_profit_pct:+.2f}%)")
            print(f"🎯 Trades: {system.total_trades}")
            print(f"🏆 Win Rate: {win_rate:.1f}%")
            
            if system.total_profit >= system.target_profit:
                print("🎉 CONSERVATIVE TARGET ACHIEVED!")
            else:
                print("⚠️ Target not reached - but losses minimized")
                
        except KeyboardInterrupt:
            print("\n🛑 Conservative trading stopped by user")
            print(f"💰 Current Profit: ${system.total_profit:+.2f}")
            
    else:
        print("❌ Failed to initialize conservative system")

if __name__ == "__main__":
    asyncio.run(main())
