"""Tests for risk management system."""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from arbtrader.risk.risk_manager import <PERSON><PERSON>anager, RiskLevel
from arbtrader.core.exchange import ExchangeManager, Balance
from arbtrader.data.market_data import MarketDataManager


@pytest.fixture
def mock_exchange_manager():
    """Create mock exchange manager."""
    exchange = Mock(spec=ExchangeManager)
    exchange.get_account_balance = AsyncMock(return_value=[
        Balance(asset="USDT", free=1000.0, locked=0.0, total=1000.0),
        Balance(asset="BTC", free=0.1, locked=0.0, total=0.1)
    ])
    return exchange


@pytest.fixture
def mock_market_data_manager():
    """Create mock market data manager."""
    market_data = Mock(spec=MarketDataManager)
    market_data.get_ticker = Mock(return_value=Mock(price=50000.0))
    market_data.get_price_history = Mock(return_value=[50000.0] * 100)
    return market_data


@pytest.fixture
def risk_manager(mock_exchange_manager, mock_market_data_manager):
    """Create risk manager instance."""
    return RiskManager(mock_exchange_manager, mock_market_data_manager)


def test_risk_manager_initialization(risk_manager):
    """Test risk manager initialization."""
    assert risk_manager.current_risk_level == RiskLevel.LOW
    assert len(risk_manager.risk_limits) > 0
    assert 'max_daily_loss' in risk_manager.risk_limits
    assert 'max_position_size' in risk_manager.risk_limits


@pytest.mark.asyncio
async def test_update_portfolio_value(risk_manager, mock_exchange_manager):
    """Test portfolio value update."""
    await risk_manager._update_portfolio_value()
    
    # Should have called get_account_balance
    mock_exchange_manager.get_account_balance.assert_called_once()
    
    # Portfolio value should be calculated (1000 USDT + 0.1 BTC * 50000)
    expected_value = 1000.0 + 0.1 * 50000.0
    assert risk_manager.portfolio_value == expected_value


def test_check_position_risk(risk_manager):
    """Test position risk checking."""
    # Set portfolio value
    risk_manager.portfolio_value = 10000.0
    
    # Test valid position
    assert risk_manager.check_position_risk("BTCUSDT", 0.01, 50000.0) is True
    
    # Test position too large
    assert risk_manager.check_position_risk("BTCUSDT", 1.0, 50000.0) is False


def test_calculate_position_size(risk_manager):
    """Test position size calculation."""
    risk_manager.portfolio_value = 10000.0
    
    # Test fixed sizing
    position_size = risk_manager.calculate_position_size("BTCUSDT", 100.0, 50000.0)
    assert position_size > 0
    
    # Test with stop loss
    position_size_with_stop = risk_manager.calculate_position_size(
        "BTCUSDT", 100.0, 50000.0, 48000.0
    )
    assert position_size_with_stop > 0


def test_should_halt_trading(risk_manager):
    """Test trading halt conditions."""
    # Normal conditions
    assert risk_manager.should_halt_trading() is False
    
    # Critical risk level
    risk_manager.current_risk_level = RiskLevel.CRITICAL
    assert risk_manager.should_halt_trading() is True
    
    # Reset and test daily loss limit
    risk_manager.current_risk_level = RiskLevel.LOW
    risk_manager.risk_limits['max_daily_loss'].is_breached = True
    assert risk_manager.should_halt_trading() is True


def test_risk_level_assessment(risk_manager):
    """Test risk level assessment."""
    # Test low risk
    risk_level = risk_manager._assess_risk_level(0.1, -1.0, -0.5)
    assert risk_level == RiskLevel.LOW
    
    # Test high risk
    risk_level = risk_manager._assess_risk_level(-5.0, -15.0, -8.0)
    assert risk_level == RiskLevel.CRITICAL


def test_exposure_tracking(risk_manager):
    """Test position exposure tracking."""
    initial_exposure = risk_manager.total_exposure
    
    # Add exposure
    risk_manager.add_position_exposure("BTCUSDT", 1000.0)
    assert risk_manager.total_exposure == initial_exposure + 1000.0
    
    # Remove exposure
    risk_manager.remove_position_exposure("BTCUSDT", 500.0)
    assert risk_manager.total_exposure == initial_exposure + 500.0


def test_get_risk_summary(risk_manager):
    """Test risk summary generation."""
    risk_manager.portfolio_value = 10000.0
    risk_manager.total_exposure = 5000.0
    
    summary = risk_manager.get_risk_summary()
    
    assert 'risk_level' in summary
    assert 'portfolio_value' in summary
    assert 'total_exposure' in summary
    assert 'exposure_ratio' in summary
    assert summary['portfolio_value'] == 10000.0
    assert summary['total_exposure'] == 5000.0
    assert summary['exposure_ratio'] == 0.5
