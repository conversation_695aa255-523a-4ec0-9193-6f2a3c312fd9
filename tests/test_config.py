"""Tests for configuration management."""

import pytest
from arbtrader.core.config import Config, TradingMode, PositionSizeMethod


def test_config_defaults():
    """Test default configuration values."""
    config = Config(
        binance_api_key="test_key",
        binance_secret_key="test_secret"
    )
    
    assert config.trading_mode == TradingMode.PAPER
    assert config.max_position_size == 1000.0
    assert config.risk_per_trade == 0.02
    assert config.enable_triangular_arbitrage is True
    assert config.enable_pairs_trading is True
    assert config.enable_basis_trading is True


def test_config_validation():
    """Test configuration validation."""
    # Test invalid risk percentage
    with pytest.raises(ValueError):
        Config(
            binance_api_key="test_key",
            binance_secret_key="test_secret",
            risk_per_trade=1.5  # Invalid: > 1
        )
    
    # Test invalid correlation threshold
    with pytest.raises(ValueError):
        Config(
            binance_api_key="test_key",
            binance_secret_key="test_secret",
            correlation_threshold=1.5  # Invalid: > 1
        )


def test_trading_mode_enum():
    """Test trading mode enumeration."""
    assert TradingMode.PAPER == "paper"
    assert TradingMode.LIVE == "live"


def test_position_size_method_enum():
    """Test position size method enumeration."""
    assert PositionSizeMethod.FIXED == "fixed"
    assert PositionSizeMethod.KELLY == "kelly"
    assert PositionSizeMethod.VOLATILITY == "volatility"


def test_config_from_env(monkeypatch):
    """Test configuration from environment variables."""
    monkeypatch.setenv("BINANCE_API_KEY", "env_key")
    monkeypatch.setenv("BINANCE_SECRET_KEY", "env_secret")
    monkeypatch.setenv("TRADING_MODE", "live")
    monkeypatch.setenv("MAX_POSITION_SIZE", "2000")
    
    config = Config()
    
    assert config.binance_api_key == "env_key"
    assert config.binance_secret_key == "env_secret"
    assert config.trading_mode == TradingMode.LIVE
    assert config.max_position_size == 2000.0
