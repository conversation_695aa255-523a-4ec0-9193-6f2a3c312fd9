#!/usr/bin/env python3
"""
EMERGENCY POSITION CLOSER
🚨 CLOSES ALL OPEN POSITIONS SAFELY 🚨
FIXES ALL MARGIN ISSUES AND RESETS ACCOUNT
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arbtrader.core.futures_exchange import FuturesExchangeManager
from arbtrader.core.config import get_config

class EmergencyPositionCloser:
    """Emergency position closer - SAFELY closes all positions!"""
    
    def __init__(self):
        self.exchange = FuturesExchangeManager()
        self.config = get_config()
        self.config.trading_mode = "live"
        
        print("🚨 EMERGENCY POSITION CLOSER")
        print("🛡️ SAFELY CLOSING ALL OPEN POSITIONS")
        print("💰 PROTECTING YOUR BALANCE")
        print("🔧 FIXING ALL MARGIN ISSUES")
    
    async def initialize(self):
        """Initialize the emergency closer."""
        print("🔌 Connecting to Binance Futures TESTNET...")
        
        if not await self.exchange.initialize():
            print("❌ Failed to connect to Binance testnet")
            return False
        
        print("✅ Connected to Binance Futures TESTNET")
        return True
    
    async def emergency_close_all_positions(self):
        """Emergency close all open positions."""
        print("\n🚨 STARTING EMERGENCY POSITION CLOSURE")
        print("🛡️ This will safely close ALL open positions")
        
        try:
            # Get all open positions
            positions = self.exchange.client.futures_position_information()
            
            open_positions = []
            for position in positions:
                position_amt = float(position['positionAmt'])
                if abs(position_amt) > 0:
                    open_positions.append(position)
            
            if not open_positions:
                print("✅ No open positions found - account is clean!")
                return True
            
            print(f"🔍 Found {len(open_positions)} open positions:")
            
            total_unrealized_pnl = 0
            
            for position in open_positions:
                symbol = position['symbol']
                position_amt = float(position['positionAmt'])
                entry_price = float(position['entryPrice'])
                mark_price = float(position['markPrice'])
                unrealized_pnl = float(position['unRealizedProfit'])
                
                total_unrealized_pnl += unrealized_pnl
                
                side = "LONG" if position_amt > 0 else "SHORT"
                
                print(f"📊 {symbol}: {side} {abs(position_amt):.6f}")
                print(f"   Entry: ${entry_price:.2f} | Mark: ${mark_price:.2f}")
                print(f"   PnL: ${unrealized_pnl:+.2f}")
            
            print(f"\n💰 Total Unrealized PnL: ${total_unrealized_pnl:+.2f}")
            
            # Close all positions
            print("\n🔧 CLOSING ALL POSITIONS...")
            
            for position in open_positions:
                symbol = position['symbol']
                position_amt = float(position['positionAmt'])
                
                if abs(position_amt) > 0:
                    # Determine close side
                    close_side = 'SELL' if position_amt > 0 else 'BUY'
                    close_quantity = abs(position_amt)
                    
                    print(f"🔧 Closing {symbol}: {close_side} {close_quantity:.6f}")
                    
                    try:
                        # Close position with market order
                        result = self.exchange.client.futures_create_order(
                            symbol=symbol,
                            side=close_side,
                            type='MARKET',
                            quantity=close_quantity,
                            reduceOnly=True
                        )
                        
                        print(f"✅ {symbol} position closed successfully")
                        print(f"   Order ID: {result['orderId']}")
                        
                    except Exception as e:
                        print(f"❌ Failed to close {symbol}: {e}")
                        
                        # Try alternative method
                        try:
                            print(f"🔄 Trying alternative close for {symbol}...")
                            result = self.exchange.client.futures_create_order(
                                symbol=symbol,
                                side=close_side,
                                type='MARKET',
                                quantity=close_quantity
                            )
                            print(f"✅ {symbol} closed with alternative method")
                        except Exception as e2:
                            print(f"❌ Alternative close failed for {symbol}: {e2}")
            
            print("\n🔧 RESETTING MARGIN MODES...")
            
            # Reset margin modes for main symbols
            main_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'LINKUSDT']
            
            for symbol in main_symbols:
                try:
                    # Set to ISOLATED margin
                    self.exchange.client.futures_change_margin_type(
                        symbol=symbol, 
                        marginType='ISOLATED'
                    )
                    print(f"✅ {symbol}: Margin set to ISOLATED")
                except Exception as e:
                    print(f"⚠️ {symbol} margin: {e}")
                
                try:
                    # Set leverage to 1x
                    self.exchange.client.futures_change_leverage(
                        symbol=symbol, 
                        leverage=1
                    )
                    print(f"✅ {symbol}: Leverage set to 1x")
                except Exception as e:
                    print(f"⚠️ {symbol} leverage: {e}")
            
            print("\n💰 FINAL ACCOUNT STATUS:")
            
            # Get final balance
            balance_info = await self.exchange.get_futures_account_balance()
            if balance_info:
                for balance in balance_info:
                    if hasattr(balance, 'asset') and balance.asset == 'USDT':
                        final_balance = float(balance.balance)
                        print(f"💰 Final Balance: ${final_balance:,.2f} USDT")
                        break
            
            # Check for remaining positions
            final_positions = self.exchange.client.futures_position_information()
            remaining_positions = [p for p in final_positions if abs(float(p['positionAmt'])) > 0]
            
            if remaining_positions:
                print(f"⚠️ {len(remaining_positions)} positions still open")
                for pos in remaining_positions:
                    print(f"   {pos['symbol']}: {pos['positionAmt']}")
            else:
                print("✅ All positions successfully closed!")
            
            print("\n🎯 EMERGENCY CLOSURE COMPLETE!")
            print("✅ Account is now clean and ready for proper trading")
            
            return True
            
        except Exception as e:
            print(f"❌ Emergency closure failed: {e}")
            return False

async def main():
    """Main function to run emergency position closer."""
    print("🚨 EMERGENCY POSITION CLOSER")
    print("⚠️ This will close ALL open positions on your Binance testnet")
    print("💰 This is to protect your balance and fix margin issues")
    
    confirm = input("\nDo you want to proceed? (yes/no): ").strip().lower()
    if confirm != 'yes':
        print("❌ Emergency closure cancelled")
        return
    
    closer = EmergencyPositionCloser()
    
    if await closer.initialize():
        await closer.emergency_close_all_positions()
    else:
        print("❌ Failed to initialize emergency closer")

if __name__ == "__main__":
    asyncio.run(main())
