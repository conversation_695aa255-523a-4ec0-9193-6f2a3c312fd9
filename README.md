# ArbTrader - Professional Cryptocurrency Arbitrage Trading System

[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

ArbTrader is a comprehensive, professional-grade cryptocurrency arbitrage trading system that implements three powerful strategies:

- **Triangular Arbitrage**: Exploits price differences across three currency pairs
- **Statistical Arbitrage (Pairs Trading)**: Uses mean reversion on correlated cryptocurrency pairs
- **Cash-and-Carry (Basis Trading)**: Captures basis spreads between spot and futures markets

## 🚀 Features

### Core Capabilities
- **Multi-Strategy Trading**: Three proven arbitrage strategies running simultaneously
- **Real-Time Market Data**: WebSocket connections for ultra-low latency data
- **Advanced Risk Management**: Comprehensive position sizing, stop-losses, and portfolio limits
- **Paper & Live Trading**: Test strategies safely before deploying real capital
- **Professional Logging**: Detailed trade logs, performance metrics, and error tracking
- **Live Dashboard**: Real-time monitoring with rich CLI interface

### Technical Excellence
- **Robust API Integration**: Rate-limited, error-handled Binance API client
- **Asynchronous Architecture**: High-performance async/await implementation
- **Comprehensive Testing**: Unit tests and integration tests included
- **Production Ready**: Professional error handling, monitoring, and alerting
- **Configurable**: Extensive configuration options for all parameters

## 📋 Prerequisites

- Python 3.9 or higher
- Binance account with API access
- Minimum 1000 USDT for live trading (recommended)

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/ArbTrader_Can.git
cd ArbTrader_Can
```

### 2. Create Virtual Environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Install TA-Lib (Required for Technical Analysis)
```bash
# On macOS
brew install ta-lib

# On Ubuntu/Debian
sudo apt-get install libta-lib-dev

# On Windows
# Download and install from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
```

### 5. Configure Environment
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:
```env
# Binance API Configuration
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=true

# Trading Configuration
TRADING_MODE=paper
MAX_POSITION_SIZE=1000
RISK_PER_TRADE=0.02
MAX_DAILY_LOSS=0.05

# Strategy Configuration
ENABLE_TRIANGULAR_ARBITRAGE=true
ENABLE_PAIRS_TRADING=true
ENABLE_BASIS_TRADING=true
```

## 🔑 Binance API Setup

### 1. Create API Keys
1. Log into your Binance account
2. Go to Account → API Management
3. Create a new API key
4. Enable "Enable Spot & Margin Trading" (for live trading)
5. Add your server IP to the whitelist (recommended)

### 2. API Permissions Required
- **Spot Trading**: For executing spot market orders
- **Read Info**: For account balance and market data
- **Futures Trading**: For basis trading strategies (optional)

### 3. Security Best Practices
- Use testnet for initial testing
- Restrict API access by IP address
- Start with small position sizes
- Monitor trades closely

## 🚀 Quick Start

### 1. Test Configuration
```bash
python -m arbtrader.cli config-check
```

### 2. Start Paper Trading
```bash
python -m arbtrader.cli start --paper
```

### 3. Start Live Trading (After Testing)
```bash
python -m arbtrader.cli start --live
```

### 4. Monitor Status
```bash
python -m arbtrader.cli status
```

## 📊 Trading Strategies

### Triangular Arbitrage
Exploits price inefficiencies across three currency pairs forming a triangle.

**Example**: BTC/USDT → ETH/BTC → ETH/USDT → USDT
- Detects opportunities in real-time
- Accounts for trading fees and slippage
- Executes trades simultaneously for minimal risk

**Configuration**:
```env
MIN_PROFIT_THRESHOLD=0.001  # 0.1% minimum profit
MAX_SLIPPAGE=0.0005        # 0.05% max slippage
```

### Statistical Arbitrage (Pairs Trading)
Uses statistical relationships between correlated cryptocurrency pairs.

**Process**:
1. Identifies highly correlated pairs (>0.8 correlation)
2. Calculates z-score of price spread
3. Enters positions when spread deviates significantly
4. Exits when spread reverts to mean

**Configuration**:
```env
CORRELATION_THRESHOLD=0.8   # Minimum correlation
ZSCORE_ENTRY=2.0           # Entry threshold
ZSCORE_EXIT=0.5            # Exit threshold
LOOKBACK_PERIOD=30         # Days for analysis
```

### Cash-and-Carry (Basis Trading)
Captures basis spreads between spot and futures markets.

**Strategy**:
- Identifies basis spreads between spot and futures
- Executes market-neutral positions
- Profits from basis convergence

**Configuration**:
```env
MIN_BASIS_SPREAD=0.002          # 0.2% minimum spread
FUNDING_RATE_THRESHOLD=0.0001   # Minimum funding rate
```

## ⚙️ Configuration

### Risk Management
```env
# Position Sizing
POSITION_SIZE_METHOD=fixed     # fixed, kelly, volatility
MAX_POSITION_SIZE=1000         # Maximum position size in USDT
RISK_PER_TRADE=0.02           # 2% risk per trade

# Portfolio Limits
MAX_DAILY_LOSS=0.05           # 5% maximum daily loss
STOP_LOSS_PERCENTAGE=0.02     # 2% stop loss
TAKE_PROFIT_PERCENTAGE=0.01   # 1% take profit
```

### Logging
```env
LOG_LEVEL=INFO                # DEBUG, INFO, WARNING, ERROR
LOG_FILE=logs/arbtrader.log   # Log file location
```

### Database
```env
DATABASE_URL=sqlite:///arbtrader.db  # SQLite database
REDIS_URL=redis://localhost:6379/0   # Redis for caching
```

## 📈 Performance Monitoring

### Real-Time Dashboard
The CLI provides a live dashboard showing:
- Portfolio value and P&L
- Active positions
- Strategy performance
- Risk metrics
- Trade history

### Log Files
- `logs/arbtrader.log`: General application logs
- `logs/trading.log`: Trade execution logs
- `logs/errors.log`: Error logs

### Metrics Tracked
- Total profit/loss
- Win rate
- Sharpe ratio
- Maximum drawdown
- Risk-adjusted returns

## 🛡️ Risk Management

### Built-in Protections
- **Position Limits**: Maximum position size per trade
- **Daily Loss Limits**: Automatic trading halt on excessive losses
- **Correlation Limits**: Prevents over-concentration in correlated assets
- **Slippage Protection**: Cancels trades with excessive slippage
- **API Rate Limiting**: Prevents API violations

### Risk Levels
- **LOW**: Normal operation
- **MEDIUM**: Increased monitoring
- **HIGH**: Reduced position sizes
- **CRITICAL**: Trading halt

## 🧪 Testing

### Run Unit Tests
```bash
pytest tests/
```

### Run Integration Tests
```bash
pytest tests/integration/
```

### Paper Trading
Always test strategies in paper trading mode before live deployment:
```bash
python -m arbtrader.cli start --paper
```

## 📁 Project Structure

```
ArbTrader_Can/
├── arbtrader/
│   ├── core/              # Core system components
│   │   ├── config.py      # Configuration management
│   │   ├── exchange.py    # Binance API integration
│   │   ├── logger.py      # Logging system
│   │   └── portfolio.py   # Portfolio management
│   ├── strategies/        # Trading strategies
│   │   ├── triangular_arbitrage.py
│   │   ├── pairs_trading.py
│   │   └── basis_trading.py
│   ├── data/             # Market data management
│   │   └── market_data.py
│   ├── risk/             # Risk management
│   │   └── risk_manager.py
│   └── cli.py            # Command-line interface
├── tests/                # Test suite
├── logs/                 # Log files
├── config/               # Configuration files
├── requirements.txt      # Python dependencies
├── .env.example         # Environment template
└── README.md            # This file
```

## 🔧 Advanced Configuration

### Custom Strategy Parameters
Edit the configuration file to customize strategy behavior:

```python
# In arbtrader/core/config.py
class Config(BaseSettings):
    # Triangular Arbitrage
    min_profit_threshold: float = 0.001
    max_slippage: float = 0.0005
    
    # Pairs Trading
    correlation_threshold: float = 0.8
    zscore_entry: float = 2.0
    zscore_exit: float = 0.5
    
    # Basis Trading
    min_basis_spread: float = 0.002
    funding_rate_threshold: float = 0.0001
```

### Database Configuration
For production deployment, consider using PostgreSQL:
```env
DATABASE_URL=postgresql://user:password@localhost/arbtrader
```

## 🚨 Important Warnings

### Financial Risk
- **Cryptocurrency trading involves significant financial risk**
- **Past performance does not guarantee future results**
- **Only trade with money you can afford to lose**
- **Start with small amounts and paper trading**

### Technical Risk
- **API connectivity issues can cause missed opportunities**
- **Market volatility can lead to unexpected losses**
- **Always monitor the system during operation**
- **Have emergency stop procedures in place**

## 📞 Support

### Getting Help
1. Check the logs for error messages
2. Review configuration settings
3. Test API connectivity
4. Verify account permissions

### Common Issues
- **API Connection Failed**: Check API keys and network connectivity
- **Insufficient Balance**: Ensure adequate USDT balance
- **No Opportunities Found**: Market conditions may not be favorable
- **High Risk Level**: Review position sizes and market volatility

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes. The authors are not responsible for any financial losses incurred through the use of this software. Cryptocurrency trading involves substantial risk and is not suitable for all investors.

---

**Happy Trading! 🚀**
